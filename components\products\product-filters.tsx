'use client'

import { useEffect, useState } from 'react'
import { useProductStore } from '@/lib/store/product-store'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Filter, Search } from 'lucide-react'
import { formatPrice, debounce } from '@/lib/utils'
import { Category } from '@/types'

interface ProductFiltersProps {
  categories: Category[]
}

export function ProductFilters({ categories }: ProductFiltersProps) {
  const {
    filters,
    searchQuery,
    setFilters,
    setSearchQuery,
    clearFilters,
    searchProducts,
  } = useProductStore()
  
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  const [priceRange, setPriceRange] = useState([0, 10000])
  const [showFilters, setShowFilters] = useState(false)
  
  // Debounced search
  const debouncedSearch = debounce((query: string) => {
    if (query.trim().length >= 2) {
      searchProducts(query)
    } else if (query.trim().length === 0) {
      setSearchQuery('')
    }
  }, 500)
  
  useEffect(() => {
    debouncedSearch(localSearchQuery)
  }, [localSearchQuery, debouncedSearch])
  
  const handleCategoryChange = (categorySlug: string, checked: boolean) => {
    if (checked) {
      setFilters({ category: categorySlug })
    } else {
      setFilters({ category: undefined })
    }
  }
  
  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange(values)
    setFilters({
      minPrice: values[0] > 0 ? values[0] : undefined,
      maxPrice: values[1] < 10000 ? values[1] : undefined,
    })
  }
  
  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split('-')
    setFilters({
      sortBy: sortBy as 'name' | 'price' | 'createdAt',
      sortOrder: sortOrder as 'asc' | 'desc',
    })
  }
  
  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.category) count++
    if (filters.minPrice || filters.maxPrice) count++
    if (filters.inStock) count++
    if (filters.featured) count++
    return count
  }
  
  const clearAllFilters = () => {
    clearFilters()
    setLocalSearchQuery('')
    setPriceRange([0, 10000])
  }
  
  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Ürün ara..."
          value={localSearchQuery}
          onChange={(e) => setLocalSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>
      
      {/* Sort and Filter Toggle */}
      <div className="flex items-center justify-between gap-4">
        <Select
          value={`${filters.sortBy}-${filters.sortOrder}`}
          onValueChange={handleSortChange}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sıralama" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="createdAt-desc">En Yeni</SelectItem>
            <SelectItem value="createdAt-asc">En Eski</SelectItem>
            <SelectItem value="price-asc">Fiyat (Düşük → Yüksek)</SelectItem>
            <SelectItem value="price-desc">Fiyat (Yüksek → Düşük)</SelectItem>
            <SelectItem value="name-asc">İsim (A → Z)</SelectItem>
            <SelectItem value="name-desc">İsim (Z → A)</SelectItem>
          </SelectContent>
        </Select>
        
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="lg:hidden"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filtreler
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary" className="ml-2">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>
      </div>
      
      {/* Active Filters */}
      {getActiveFiltersCount() > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm font-medium">Aktif Filtreler:</span>
          
          {filters.category && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Kategori: {categories.find(c => c.slug === filters.category)?.name}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters({ category: undefined })}
              />
            </Badge>
          )}
          
          {(filters.minPrice || filters.maxPrice) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Fiyat: {formatPrice(filters.minPrice || 0)} - {formatPrice(filters.maxPrice || 10000)}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters({ minPrice: undefined, maxPrice: undefined })}
              />
            </Badge>
          )}
          
          {filters.inStock && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Stokta Var
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters({ inStock: false })}
              />
            </Badge>
          )}
          
          {filters.featured && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Öne Çıkan
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setFilters({ featured: false })}
              />
            </Badge>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700"
          >
            Tümünü Temizle
          </Button>
        </div>
      )}
      
      {/* Filters Panel */}
      <div className={`space-y-4 ${showFilters ? 'block' : 'hidden lg:block'}`}>
        {/* Categories */}
        {categories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Kategoriler</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`category-${category.id}`}
                    checked={filters.category === category.slug}
                    onCheckedChange={(checked) => 
                      handleCategoryChange(category.slug, checked as boolean)
                    }
                  />
                  <Label
                    htmlFor={`category-${category.id}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    {category.name}
                    {category._count?.products && (
                      <span className="text-muted-foreground ml-1">
                        ({category._count.products})
                      </span>
                    )}
                  </Label>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
        
        {/* Price Range */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Fiyat Aralığı</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Slider
              value={priceRange}
              onValueChange={handlePriceRangeChange}
              max={10000}
              min={0}
              step={50}
              className="w-full"
            />
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>{formatPrice(priceRange[0])}</span>
              <span>{formatPrice(priceRange[1])}</span>
            </div>
          </CardContent>
        </Card>
        
        {/* Other Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Diğer Filtreler</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="in-stock"
                checked={filters.inStock || false}
                onCheckedChange={(checked) => setFilters({ inStock: checked as boolean })}
              />
              <Label htmlFor="in-stock" className="text-sm font-normal cursor-pointer">
                Sadece Stokta Olanlar
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={filters.featured || false}
                onCheckedChange={(checked) => setFilters({ featured: checked as boolean })}
              />
              <Label htmlFor="featured" className="text-sm font-normal cursor-pointer">
                Öne Çıkan Ürünler
              </Label>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
