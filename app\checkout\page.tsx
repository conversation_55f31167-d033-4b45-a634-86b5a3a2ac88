'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  CreditCard, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  Lock,
  ShoppingBag,
  ArrowLeft,
  CheckCircle
} from 'lucide-react'
import { useAuthStore } from '@/lib/store/auth-store'
import { useCartStore } from '@/lib/store/cart-store'
import { formatPrice } from '@/lib/utils'
import toast from 'react-hot-toast'

const checkoutSchema = z.object({
  // Billing Address
  firstName: z.string().min(2, 'Ad en az 2 karakter olmalıdır'),
  lastName: z.string().min(2, 'Soyad en az 2 karakter olmalıdır'),
  email: z.string().email('Geçerli bir e-posta adresi girin'),
  phone: z.string().min(10, 'Geçerli bir telefon numarası girin'),
  address: z.string().min(10, 'Adres en az 10 karakter olmalıdır'),
  city: z.string().min(2, 'Şehir gereklidir'),
  district: z.string().min(2, 'İlçe gereklidir'),
  postalCode: z.string().min(5, 'Posta kodu gereklidir'),
  
  // Payment
  paymentMethod: z.enum(['credit_card', 'bank_transfer', 'cash_on_delivery']),
  
  // Credit Card (conditional)
  cardNumber: z.string().optional(),
  cardExpiry: z.string().optional(),
  cardCvc: z.string().optional(),
  cardName: z.string().optional(),
  
  // Terms
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'Kullanım koşullarını kabul etmelisiniz'
  }),
  
  // Newsletter
  subscribeNewsletter: z.boolean().optional(),
})

type CheckoutForm = z.infer<typeof checkoutSchema>

export default function CheckoutPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const { items, totalItems, finalPrice, clearCart } = useCartStore()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CheckoutForm>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      firstName: user?.name?.split(' ')[0] || '',
      lastName: user?.name?.split(' ').slice(1).join(' ') || '',
      email: user?.email || '',
      paymentMethod: 'credit_card',
      acceptTerms: false,
      subscribeNewsletter: false,
    }
  })
  
  const paymentMethod = watch('paymentMethod')
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/checkout')
      return
    }
    
    if (items.length === 0) {
      router.push('/cart')
      return
    }
  }, [isAuthenticated, items.length, router])
  
  const onSubmit = async (data: CheckoutForm) => {
    setIsSubmitting(true)
    
    try {
      // Simulate order creation
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Create order
      const orderData = {
        items: items.map(item => ({
          productId: item.product.id,
          quantity: item.quantity,
          price: item.product.price
        })),
        billingAddress: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          address: data.address,
          city: data.city,
          district: data.district,
          postalCode: data.postalCode,
        },
        paymentMethod: data.paymentMethod,
        totalAmount: finalPrice,
      }
      
      // Here you would call your order creation API
      console.log('Order data:', orderData)
      
      // Clear cart
      clearCart()
      
      // Show success message
      toast.success('Siparişiniz başarıyla oluşturuldu!')
      
      // Redirect to success page
      router.push('/checkout/success?order=' + Date.now())
      
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error('Sipariş oluşturulurken hata oluştu')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  if (!isAuthenticated || items.length === 0) {
    return null
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">Ödeme</h1>
            <Badge variant="secondary">
              {totalItems} ürün
            </Badge>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Checkout Form */}
              <div className="lg:col-span-2 space-y-6">
                {/* Billing Address */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MapPin className="h-5 w-5 mr-2" />
                      Fatura Adresi
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">Ad *</Label>
                        <Input
                          id="firstName"
                          {...register('firstName')}
                          placeholder="Adınız"
                        />
                        {errors.firstName && (
                          <p className="text-sm text-red-600">{errors.firstName.message}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Soyad *</Label>
                        <Input
                          id="lastName"
                          {...register('lastName')}
                          placeholder="Soyadınız"
                        />
                        {errors.lastName && (
                          <p className="text-sm text-red-600">{errors.lastName.message}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">E-posta *</Label>
                        <Input
                          id="email"
                          type="email"
                          {...register('email')}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <p className="text-sm text-red-600">{errors.email.message}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="phone">Telefon *</Label>
                        <Input
                          id="phone"
                          {...register('phone')}
                          placeholder="0532 123 45 67"
                        />
                        {errors.phone && (
                          <p className="text-sm text-red-600">{errors.phone.message}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="address">Adres *</Label>
                      <Input
                        id="address"
                        {...register('address')}
                        placeholder="Mahalle, sokak, bina no, daire no"
                      />
                      {errors.address && (
                        <p className="text-sm text-red-600">{errors.address.message}</p>
                      )}
                    </div>
                    
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="city">Şehir *</Label>
                        <Input
                          id="city"
                          {...register('city')}
                          placeholder="İstanbul"
                        />
                        {errors.city && (
                          <p className="text-sm text-red-600">{errors.city.message}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="district">İlçe *</Label>
                        <Input
                          id="district"
                          {...register('district')}
                          placeholder="Kadıköy"
                        />
                        {errors.district && (
                          <p className="text-sm text-red-600">{errors.district.message}</p>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="postalCode">Posta Kodu *</Label>
                        <Input
                          id="postalCode"
                          {...register('postalCode')}
                          placeholder="34710"
                        />
                        {errors.postalCode && (
                          <p className="text-sm text-red-600">{errors.postalCode.message}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Payment Method */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <CreditCard className="h-5 w-5 mr-2" />
                      Ödeme Yöntemi
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RadioGroup
                      value={paymentMethod}
                      onValueChange={(value) => setValue('paymentMethod', value as any)}
                    >
                      <div className="flex items-center space-x-2 p-4 border rounded-lg">
                        <RadioGroupItem value="credit_card" id="credit_card" />
                        <Label htmlFor="credit_card" className="flex-1 cursor-pointer">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <CreditCard className="h-4 w-4 mr-2" />
                              Kredi/Banka Kartı
                            </div>
                            <div className="flex gap-1">
                              <div className="w-8 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center">V</div>
                              <div className="w-8 h-5 bg-red-600 rounded text-white text-xs flex items-center justify-center">M</div>
                            </div>
                          </div>
                        </Label>
                      </div>
                      
                      <div className="flex items-center space-x-2 p-4 border rounded-lg">
                        <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                        <Label htmlFor="bank_transfer" className="flex-1 cursor-pointer">
                          Havale/EFT
                        </Label>
                      </div>
                      
                      <div className="flex items-center space-x-2 p-4 border rounded-lg">
                        <RadioGroupItem value="cash_on_delivery" id="cash_on_delivery" />
                        <Label htmlFor="cash_on_delivery" className="flex-1 cursor-pointer">
                          Kapıda Ödeme
                        </Label>
                      </div>
                    </RadioGroup>
                    
                    {/* Credit Card Form */}
                    {paymentMethod === 'credit_card' && (
                      <div className="mt-6 p-4 bg-gray-50 rounded-lg space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="cardName">Kart Üzerindeki İsim</Label>
                          <Input
                            id="cardName"
                            {...register('cardName')}
                            placeholder="JOHN DOE"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="cardNumber">Kart Numarası</Label>
                          <Input
                            id="cardNumber"
                            {...register('cardNumber')}
                            placeholder="1234 5678 9012 3456"
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="cardExpiry">Son Kullanma</Label>
                            <Input
                              id="cardExpiry"
                              {...register('cardExpiry')}
                              placeholder="MM/YY"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="cardCvc">CVC</Label>
                            <Input
                              id="cardCvc"
                              {...register('cardCvc')}
                              placeholder="123"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {/* Terms and Newsletter */}
                <Card>
                  <CardContent className="pt-6 space-y-4">
                    <div className="flex items-start space-x-2">
                      <Checkbox
                        id="acceptTerms"
                        {...register('acceptTerms')}
                        onCheckedChange={(checked) => setValue('acceptTerms', !!checked)}
                      />
                      <Label htmlFor="acceptTerms" className="text-sm leading-relaxed">
                        <span className="text-red-500">*</span> Kullanım koşullarını ve gizlilik politikasını okudum, kabul ediyorum.
                      </Label>
                    </div>
                    {errors.acceptTerms && (
                      <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
                    )}
                    
                    <div className="flex items-start space-x-2">
                      <Checkbox
                        id="subscribeNewsletter"
                        {...register('subscribeNewsletter')}
                        onCheckedChange={(checked) => setValue('subscribeNewsletter', !!checked)}
                      />
                      <Label htmlFor="subscribeNewsletter" className="text-sm leading-relaxed">
                        Kampanya ve fırsatlardan haberdar olmak istiyorum.
                      </Label>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Order Summary */}
              <div className="lg:col-span-1">
                <Card className="sticky top-4">
                  <CardHeader>
                    <CardTitle>Sipariş Özeti</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Items */}
                    <div className="space-y-3">
                      {items.map((item) => (
                        <div key={item.id} className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <ShoppingBag className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {item.product.name}
                            </p>
                            <p className="text-xs text-gray-600">
                              {item.quantity} x {formatPrice(item.product.price)}
                            </p>
                          </div>
                          <div className="text-sm font-medium">
                            {formatPrice(item.product.price * item.quantity)}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <Separator />
                    
                    {/* Totals */}
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Kargo</span>
                        <span className="text-green-600">Ücretsiz</span>
                      </div>
                      
                      <div className="flex justify-between text-lg font-semibold">
                        <span>Toplam</span>
                        <span>{formatPrice(finalPrice)}</span>
                      </div>
                    </div>
                    
                    <Button 
                      type="submit"
                      className="w-full"
                      size="lg"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        'Sipariş Oluşturuluyor...'
                      ) : (
                        <>
                          <Lock className="h-4 w-4 mr-2" />
                          Siparişi Tamamla
                        </>
                      )}
                    </Button>
                    
                    {/* Security Info */}
                    <div className="text-center text-xs text-gray-600">
                      <Lock className="h-3 w-3 inline mr-1" />
                      256-bit SSL ile güvenli ödeme
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
