'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/lib/store/auth-store'
import { useCartStore } from '@/lib/store/cart-store'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { initialize } = useAuthStore()
  const { fetchCart } = useCartStore()
  
  useEffect(() => {
    const initializeAuth = async () => {
      await initialize()
      await fetchCart()
    }
    
    initializeAuth()
  }, [initialize, fetchCart])
  
  return <>{children}</>
}
