'use client'

import { useEffect } from 'react'
import { useProductStore } from '@/lib/store/product-store'
import { ProductCard } from './product-card'
import { ProductGridSkeleton } from './product-grid-skeleton'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface ProductGridProps {
  showPagination?: boolean
}

export function ProductGrid({ showPagination = true }: ProductGridProps) {
  const {
    products,
    isLoading,
    pagination,
    fetchProducts,
    setFilters,
    filters,
  } = useProductStore()
  
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts, filters])
  
  const handlePageChange = (newPage: number) => {
    setFilters({ page: newPage })
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
  
  if (isLoading) {
    return <ProductGridSkeleton />
  }
  
  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Ürün bulunamadı
          </h3>
          <p className="text-gray-500">
            Aradığınız kriterlere uygun ürün bulunmamaktadır. Lütfen filtreleri değiştirip tekrar deneyin.
          </p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
      
      {/* Pagination */}
      {showPagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={!pagination.hasPrev}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Önceki
          </Button>
          
          <div className="flex items-center space-x-1">
            {[...Array(pagination.totalPages)].map((_, index) => {
              const page = index + 1
              const isCurrentPage = page === pagination.page
              const showPage = 
                page === 1 ||
                page === pagination.totalPages ||
                (page >= pagination.page - 1 && page <= pagination.page + 1)
              
              if (!showPage) {
                if (page === pagination.page - 2 || page === pagination.page + 2) {
                  return <span key={page} className="px-2">...</span>
                }
                return null
              }
              
              return (
                <Button
                  key={page}
                  variant={isCurrentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                  className="min-w-[40px]"
                >
                  {page}
                </Button>
              )
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={!pagination.hasNext}
          >
            Sonraki
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
      
      {/* Results Info */}
      <div className="text-center text-sm text-muted-foreground">
        {pagination.total > 0 && (
          <p>
            {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} arası, 
            toplam {pagination.total} ürün gösteriliyor
          </p>
        )}
      </div>
    </div>
  )
}
