# E-Marketing - Modern E-Ticaret Platformu

Modern, güvenli ve kullanıcı dostu e-ticaret deneyimi sunan tam özellikli online alışveriş platformu.

## 🚀 Özellikler

### 🛍️ Ürün Yönetimi
- **Gelişmiş Ürün Kataloğu**: <PERSON><PERSON><PERSON>, filtreler ve arama
- **Ürün Varyantları**: Farklı boyut, renk ve özellikler
- **Stok Takibi**: Gerçek zamanlı stok yönetimi
- **Ürün İncelemeleri**: Kullanıcı değerlendirmeleri ve puanlama

### 👤 Kullanıcı Deneyimi
- **Güvenli Kimlik Doğrulama**: Supabase Auth ile
- **Kullanıcı Profilleri**: Kişisel bilgiler ve tercihler
- **Adres Yönetimi**: Çoklu teslimat adresleri
- **Favoriler**: Beğenilen ürünleri kaydetme

### 🛒 Alışveriş Deneyimi
- **Akıllı Sepet**: Gerçek zamanlı güncelleme
- **Hızlı Ödeme**: Stripe entegrasyonu
- **Kargo Hesaplama**: Otomatik kargo ücreti
- **Sipariş Takibi**: Detaylı sipariş durumu

### 📱 Responsive Tasarım
- **Mobil Uyumlu**: Tüm cihazlarda mükemmel görünüm
- **Modern UI**: Tailwind CSS ile şık tasarım
- **Hızlı Yükleme**: Optimize edilmiş performans

## 🛠️ Teknoloji Stack

### Frontend
- **Next.js 14**: React framework
- **TypeScript**: Tip güvenliği
- **Tailwind CSS**: Utility-first CSS
- **Radix UI**: Erişilebilir UI bileşenleri
- **Zustand**: State management

### Backend
- **Supabase**: Backend-as-a-Service
- **PostgreSQL**: Veritabanı
- **Prisma**: ORM
- **Stripe**: Ödeme işlemleri

### Geliştirme Araçları
- **ESLint**: Kod kalitesi
- **Prettier**: Kod formatı
- **Husky**: Git hooks

## 📦 Kurulum

### Gereksinimler
- Node.js 18+ 
- npm veya yarn
- PostgreSQL veritabanı

### 1. Projeyi Klonlayın
```bash
git clone https://github.com/your-username/e-marketing.git
cd e-marketing
```

### 2. Bağımlılıkları Yükleyin
```bash
npm install
# veya
yarn install
```

### 3. Ortam Değişkenlerini Ayarlayın
`.env.local` dosyasını oluşturun:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Database
DATABASE_URL=your_postgresql_connection_string

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
```

### 4. Veritabanını Ayarlayın
```bash
# Prisma şemasını oluştur
npx prisma generate

# Veritabanı migrasyonlarını çalıştır
npx prisma db push

# (Opsiyonel) Prisma Studio'yu başlat
npx prisma studio
```

### 5. Geliştirme Sunucusunu Başlatın
```bash
npm run dev
# veya
yarn dev
```

Uygulama [http://localhost:3000](http://localhost:3000) adresinde çalışacaktır.

## 🗂️ Proje Yapısı

```
e-marketing/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Kimlik doğrulama sayfaları
│   ├── (shop)/            # Alışveriş sayfaları
│   ├── api/               # API routes
│   └── globals.css        # Global stiller
├── components/            # React bileşenleri
│   ├── ui/               # Temel UI bileşenleri
│   ├── layout/           # Layout bileşenleri
│   ├── products/         # Ürün bileşenleri
│   └── cart/             # Sepet bileşenleri
├── lib/                  # Yardımcı kütüphaneler
│   ├── store/           # Zustand stores
│   ├── supabase/        # Supabase konfigürasyonu
│   └── stripe/          # Stripe konfigürasyonu
├── types/               # TypeScript tip tanımları
├── prisma/             # Prisma şeması
└── public/             # Statik dosyalar
```

## 🔧 Konfigürasyon

### Supabase Kurulumu
1. [Supabase](https://supabase.com) hesabı oluşturun
2. Yeni proje oluşturun
3. Database URL ve API anahtarlarını alın
4. Authentication ayarlarını yapılandırın

### Stripe Kurulumu
1. [Stripe](https://stripe.com) hesabı oluşturun
2. API anahtarlarını alın
3. Webhook endpoint'ini ayarlayın
4. Test kartlarını kullanarak test edin

## 📱 Kullanım

### Yönetici Paneli
- Ürün ekleme/düzenleme
- Sipariş yönetimi
- Kullanıcı yönetimi
- Raporlar ve analizler

### Müşteri Deneyimi
- Ürün arama ve filtreleme
- Sepete ekleme
- Güvenli ödeme
- Sipariş takibi

## 🧪 Test

```bash
# Unit testleri çalıştır
npm run test

# E2E testleri çalıştır
npm run test:e2e

# Test coverage
npm run test:coverage
```

## 🚀 Deployment

### Vercel (Önerilen)
1. GitHub'a push yapın
2. Vercel'e import edin
3. Ortam değişkenlerini ayarlayın
4. Deploy edin

### Diğer Platformlar
- Netlify
- Railway
- DigitalOcean App Platform

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

## 🔧 Admin Panel

### Admin Kullanıcısı Oluşturma
```bash
# Script ile otomatik oluşturma
npm run setup:admin

# Veya manuel olarak
# http://localhost:3000/admin/setup adresine gidin
```

**Admin Giriş Bilgileri:**
- E-posta: <EMAIL>
- Şifre: admin123
- Kurulum Anahtarı: demo-setup-key-2024

### Admin Panel Özellikleri
- 📊 **Dashboard**: Satış istatistikleri ve genel bakış
- 📦 **Ürün Yönetimi**: CRUD işlemleri, stok takibi
- 🛒 **Sipariş Yönetimi**: Sipariş durumu güncelleme
- 👥 **Kullanıcı Yönetimi**: Kullanıcı durumu kontrolü
- 🔐 **Güvenlik**: Role-based access control

### Test Rehberi
Detaylı test talimatları için [ADMIN_PANEL_TEST_GUIDE.md](./ADMIN_PANEL_TEST_GUIDE.md) dosyasını inceleyin.

## 🧪 Demo Veriler

Demo verileri yüklemek için Supabase SQL Editor'da `scripts/demo-data.sql` dosyasını çalıştırın.

**Demo İçeriği:**
- 5 kategori
- 10 ürün (görseller dahil)
- 5 demo kullanıcı
- 5 demo sipariş
- Adres ve profil bilgileri

## 📞 İletişim

- **Email**: <EMAIL>
- **Website**: https://e-marketing.com
- **GitHub**: https://github.com/your-username/e-marketing

## 🙏 Teşekkürler

- [Next.js](https://nextjs.org/) - React framework
- [Supabase](https://supabase.com/) - Backend platform
- [Stripe](https://stripe.com/) - Ödeme altyapısı
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Radix UI](https://www.radix-ui.com/) - UI primitives

---

⭐ Bu projeyi beğendiyseniz yıldız vermeyi unutmayın!
