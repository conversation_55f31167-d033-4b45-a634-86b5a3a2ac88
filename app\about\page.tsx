'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useDataBridge } from '@/lib/hooks/use-data-bridge'
import {
  ShoppingBag,
  Users,
  Award,
  Truck,
  Shield,
  Star,
  Heart,
  Globe,
  Clock,
  CheckCircle
} from 'lucide-react'

export default function AboutPage() {
  const { isLoaded, getPageContent, getSiteSettings } = useDataBridge()
  const [aboutContent, setAboutContent] = useState({})
  const [siteSettings, setSiteSettings] = useState({})

  useEffect(() => {
    if (isLoaded) {
      setAboutContent(getPageContent('about'))
      setSiteSettings(getSiteSettings())
    }
  }, [isLoaded, getPageContent, getSiteSettings])
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section
        className="text-white py-20"
        style={{
          background: `linear-gradient(to right, ${siteSettings.primaryColor || '#3b82f6'}, ${siteSettings.secondaryColor || '#8b5cf6'})`
        }}
      >
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            {aboutContent.title || 'Hakkımızda'}
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            {aboutContent.subtitle || '2024 yılında kurulan E-Marketing, modern e-ticaret deneyimi sunan yenilikçi bir platformdur. Müşteri memnuniyetini ön planda tutarak, kaliteli ürünleri uygun fiyatlarla sunmayı hedefliyoruz.'}
          </p>
          <Badge variant="secondary" className="text-lg px-6 py-2">
            Güvenilir E-Ticaret Platformu
          </Badge>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12">
            <Card>
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Heart className="h-8 w-8 text-red-500 mr-3" />
                  <h2 className="text-2xl font-bold">Misyonumuz</h2>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {aboutContent.mission || 'Müşterilerimize en kaliteli ürünleri, en uygun fiyatlarla sunarak online alışveriş deneyimini mükemmelleştirmek. Güvenilir, hızlı ve kullanıcı dostu bir platform ile e-ticaret sektöründe fark yaratmak.'}
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Globe className="h-8 w-8 text-blue-500 mr-3" />
                  <h2 className="text-2xl font-bold">Vizyonumuz</h2>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {aboutContent.vision || 'Türkiye\'nin en güvenilir ve tercih edilen e-ticaret platformu olmak. Teknoloji ve inovasyonu kullanarak müşteri deneyimini sürekli geliştirmek ve sektörde öncü konumda yer almak.'}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Değerlerimiz</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardContent className="p-6">
                <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Güvenilirlik</h3>
                <p className="text-gray-600">
                  Müşteri bilgilerinin güvenliği ve gizliliği bizim için önceliktir.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Star className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Kalite</h3>
                <p className="text-gray-600">
                  Sadece kaliteli ve test edilmiş ürünleri müşterilerimizle buluşturuyoruz.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Clock className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Hızlı Teslimat</h3>
                <p className="text-gray-600">
                  Siparişlerinizi en kısa sürede ve güvenli şekilde teslim ediyoruz.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Users className="h-12 w-12 text-purple-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Müşteri Odaklılık</h3>
                <p className="text-gray-600">
                  Müşteri memnuniyeti tüm süreçlerimizin merkezinde yer alır.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Rakamlarla E-Marketing</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">10K+</div>
              <div className="text-gray-600">Mutlu Müşteri</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-green-600 mb-2">5K+</div>
              <div className="text-gray-600">Ürün Çeşidi</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-purple-600 mb-2">99%</div>
              <div className="text-gray-600">Müşteri Memnuniyeti</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-orange-600 mb-2">24/7</div>
              <div className="text-gray-600">Müşteri Desteği</div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Neden E-Marketing?</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold mb-2">Geniş Ürün Yelpazesi</h3>
                <p className="text-gray-600">Elektronikten giyime, ev eşyasından spora kadar her kategoride binlerce ürün.</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold mb-2">Güvenli Ödeme</h3>
                <p className="text-gray-600">SSL sertifikası ve güvenli ödeme sistemleri ile korumalı alışveriş.</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold mb-2">Hızlı Kargo</h3>
                <p className="text-gray-600">Türkiye geneli hızlı ve güvenli kargo ile aynı gün teslimat seçeneği.</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold mb-2">Kolay İade</h3>
                <p className="text-gray-600">14 gün içinde koşulsuz iade garantisi ile risk almadan alışveriş yapın.</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold mb-2">7/24 Destek</h3>
                <p className="text-gray-600">Uzman müşteri hizmetleri ekibimiz her zaman yanınızda.</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <CheckCircle className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-semibold mb-2">En İyi Fiyat</h3>
                <p className="text-gray-600">Rekabetçi fiyatlar ve özel indirimlerle en uygun fiyat garantisi.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Bizimle İletişime Geçin</h2>
          <p className="text-xl mb-8">
            Sorularınız mı var? Müşteri hizmetleri ekibimiz size yardımcı olmaya hazır.
          </p>
          <div className="space-x-4">
            <a 
              href="/contact" 
              className="inline-block bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              İletişim
            </a>
            <a 
              href="tel:+902121234567" 
              className="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors"
            >
              (0212) 123 45 67
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
