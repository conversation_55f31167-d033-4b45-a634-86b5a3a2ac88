# Admin Panel Test Rehberi

Bu rehber, E-Marketing admin panel sistemini test etmek için gerekli adımları içerir.

## 🚀 Hızlı Başlangıç

### 1. <PERSON><PERSON>yi <PERSON>ı<PERSON>
```bash
npm run dev
```

### 2. Demo Verileri Yükleyin
Supabase SQL Editor'da `scripts/demo-data.sql` dosyasını çalıştırın.

### 3. Admin Kullanıcısı Oluşturun
İki yöntemden birini kullanın:

**Yöntem A: Web Arayüzü**
1. http://localhost:3000/admin/setup adresine gidin
2. Aşağıdaki bilgileri girin:
   - Ad: Admin User
   - E-posta: <EMAIL>
   - Şifre: admin123
   - Kurulum Anahtarı: demo-setup-key-2024

**Yöntem B: Script (Node.js gerekli)**
```bash
node scripts/create-admin.js
```

## 🔐 Giriş Bilgileri

### Admin He<PERSON>bı
- **URL**: http://localhost:3000/admin
- **E-posta**: <EMAIL>
- **Şifre**: admin123

### Demo Müşteri Hesapları
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

*Not: Müşteri hesapları için şifre Supabase Auth'da manuel olarak ayarlanmalıdır.*

## 📋 Test Senaryoları

### 1. Admin Panel Erişimi
- [ ] Admin setup sayfası çalışıyor (/admin/setup)
- [ ] Admin kullanıcısı oluşturuluyor
- [ ] Admin paneline giriş yapılabiliyor (/admin)
- [ ] Normal kullanıcılar admin paneline erişemiyor

### 2. Ana Sayfa Navigation
- [ ] Header'daki "Giriş" butonu /auth/login'e yönlendiriyor
- [ ] Header'daki "Kayıt" butonu /auth/register'e yönlendiriyor
- [ ] "Hakkımızda" linki /about sayfasını açıyor
- [ ] "İletişim" linki /contact sayfasını açıyor
- [ ] "Kategoriler" linki /categories sayfasını açıyor
- [ ] "Ürünler" linki /shop sayfasını açıyor
- [ ] Logo ana sayfaya yönlendiriyor

### 2. Dashboard
- [ ] Dashboard yükleniyor
- [ ] İstatistik kartları görüntüleniyor
- [ ] Son siparişler listeleniyor
- [ ] En çok satan ürünler görüntüleniyor
- [ ] Uyarı kartları (bekleyen siparişler, düşük stok) çalışıyor

### 3. Ürün Yönetimi (/admin/products)
- [ ] Ürün listesi yükleniyor
- [ ] Arama fonksiyonu çalışıyor
- [ ] Ürün detayları görüntüleniyor
- [ ] Ürün düzenleme sayfası açılıyor
- [ ] Ürün silme işlemi çalışıyor
- [ ] Yeni ürün ekleme sayfası açılıyor

### 4. Sipariş Yönetimi (/admin/orders)
- [ ] Sipariş listesi yükleniyor
- [ ] Sipariş filtreleme çalışıyor
- [ ] Sipariş detayları görüntüleniyor
- [ ] Sipariş durumu güncellenebiliyor
- [ ] Sipariş arama fonksiyonu çalışıyor

### 5. Kullanıcı Yönetimi (/admin/users)
- [ ] Kullanıcı listesi yükleniyor
- [ ] Kullanıcı filtreleme çalışıyor
- [ ] Kullanıcı detayları görüntüleniyor
- [ ] Kullanıcı durumu güncellenebiliyor (aktif/pasif)
- [ ] Kullanıcı arama fonksiyonu çalışıyor

### 6. Navigation ve Layout
- [ ] Sidebar navigation çalışıyor
- [ ] Mobile responsive tasarım çalışıyor
- [ ] Kullanıcı dropdown menüsü çalışıyor
- [ ] Çıkış yapma işlemi çalışıyor
- [ ] Ana siteye geçiş linki çalışıyor

### 7. Yeni Sayfalar
- [ ] /about sayfası yükleniyor ve içerik görüntüleniyor
- [ ] /contact sayfası yükleniyor ve form çalışıyor
- [ ] /categories sayfası yükleniyor ve kategoriler listeleniyor
- [ ] /shop sayfası yükleniyor ve ürünler görüntüleniyor
- [ ] /profile sayfası yükleniyor (giriş yapılı kullanıcı için)
- [ ] /orders sayfası yükleniyor (giriş yapılı kullanıcı için)

### 8. Auth Sayfaları
- [ ] /auth/login sayfası yükleniyor ve giriş formu çalışıyor
- [ ] /auth/register sayfası yükleniyor ve kayıt formu çalışıyor
- [ ] Giriş yapmamış kullanıcılar korumalı sayfalara erişemiyor
- [ ] Giriş sonrası redirect çalışıyor

## 🐛 Bilinen Sorunlar ve Çözümler

### Supabase Bağlantı Sorunu
```
Error: Invalid API key
```
**Çözüm**: `.env.local` dosyasında Supabase URL ve API key'lerini kontrol edin.

### Admin Kullanıcısı Oluşturulamıyor
```
Error: Geçersiz kurulum anahtarı
```
**Çözüm**: Kurulum anahtarının `demo-setup-key-2024` olduğundan emin olun.

### Sayfa Yüklenmiyor
```
Error: 403 Forbidden
```
**Çözüm**: Admin kullanıcısının role'ünün 'ADMIN' olduğunu Supabase'de kontrol edin.

### Demo Veriler Görünmüyor
**Çözüm**: `scripts/demo-data.sql` dosyasını Supabase SQL Editor'da çalıştırdığınızdan emin olun.

## 📊 Test Verileri

### Kategoriler
- Elektronik (5 ürün)
- Giyim (2 ürün)
- Ev & Yaşam (2 ürün)
- Spor & Outdoor (2 ürün)
- Kitap & Hobi (1 ürün)

### Ürünler
- 10 demo ürün
- Farklı fiyat aralıkları
- Stok durumları
- Ürün görselleri (Unsplash)

### Siparişler
- 5 demo sipariş
- Farklı durumlar (PENDING, CONFIRMED, PROCESSING, SHIPPED, DELIVERED)
- Farklı ödeme durumları
- Sipariş geçmişi

### Kullanıcılar
- 5 demo müşteri
- Profil bilgileri
- Adres bilgileri
- Sipariş geçmişi

## 🔧 Geliştirici Notları

### API Endpoint'leri
- `GET /api/admin/dashboard` - Dashboard verileri
- `GET /api/admin/products` - Ürün listesi
- `GET /api/admin/orders` - Sipariş listesi
- `GET /api/admin/users` - Kullanıcı listesi
- `POST /api/admin/setup` - Admin kullanıcısı oluşturma

### Veritabanı Tabloları
- `users` - Kullanıcı bilgileri
- `products` - Ürün bilgileri
- `categories` - Kategori bilgileri
- `orders` - Sipariş bilgileri
- `order_items` - Sipariş kalemleri
- `addresses` - Adres bilgileri
- `profiles` - Kullanıcı profilleri

### Güvenlik
- Role-based access control (RBAC)
- Admin route koruması
- API endpoint yetkilendirmesi
- Supabase Row Level Security (RLS)

## 📞 Destek

Sorun yaşarsanız:
1. Browser console'u kontrol edin
2. Network tab'ında API çağrılarını kontrol edin
3. Supabase logs'ları kontrol edin
4. `.env.local` dosyasını kontrol edin

## ✅ Test Tamamlandı

Tüm test senaryoları başarıyla tamamlandığında, admin panel sistemi production'a hazırdır.
