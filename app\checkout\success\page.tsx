'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  Package, 
  Mail, 
  Phone,
  Home,
  ShoppingBag,
  Download,
  Calendar
} from 'lucide-react'
import { formatPrice } from '@/lib/utils'

export default function CheckoutSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [orderNumber, setOrderNumber] = useState('')
  
  useEffect(() => {
    const order = searchParams.get('order')
    if (order) {
      setOrderNumber(`ORD-${order}`)
    } else {
      // If no order parameter, redirect to home
      router.push('/')
    }
  }, [searchParams, router])
  
  const estimatedDelivery = new Date()
  estimatedDelivery.setDate(estimatedDelivery.getDate() + 3)
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Siparişiniz Alındı!
            </h1>
            <p className="text-gray-600">
              Siparişiniz başarıyla oluşturuldu ve işleme alındı.
            </p>
          </div>
          
          {/* Order Details */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Sipariş Detayları</span>
                <Badge variant="secondary">{orderNumber}</Badge>
              </CardTitle>
              <CardDescription>
                Sipariş bilgileriniz e-posta adresinize gönderildi.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Sipariş Tarihi</p>
                    <p className="text-sm text-gray-600">
                      {new Date().toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Package className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Tahmini Teslimat</p>
                    <p className="text-sm text-gray-600">
                      {estimatedDelivery.toLocaleDateString('tr-TR')}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Sipariş Durumu</span>
                  <Badge className="bg-blue-100 text-blue-800">
                    Hazırlanıyor
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Next Steps */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Sırada Ne Var?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-semibold text-blue-600">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Sipariş Onayı</h4>
                    <p className="text-sm text-gray-600">
                      Siparişinizi inceleyip onaylıyoruz (1-2 saat)
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-semibold text-gray-600">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Hazırlık</h4>
                    <p className="text-sm text-gray-600">
                      Ürünlerinizi özenle paketliyoruz (1-2 gün)
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-semibold text-gray-600">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Kargo</h4>
                    <p className="text-sm text-gray-600">
                      Kargo firmasına teslim ediyoruz (1-2 gün)
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-sm font-semibold text-gray-600">4</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Teslimat</h4>
                    <p className="text-sm text-gray-600">
                      Siparişiniz adresinize teslim edilir
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Contact Info */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>İletişim</CardTitle>
              <CardDescription>
                Sorularınız için bizimle iletişime geçebilirsiniz.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">E-posta</p>
                    <a 
                      href="mailto:<EMAIL>"
                      className="text-sm text-primary hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Telefon</p>
                    <a 
                      href="tel:+902121234567"
                      className="text-sm text-primary hover:underline"
                    >
                      (0212) 123 45 67
                    </a>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild className="flex-1">
              <Link href="/orders">
                <Package className="h-4 w-4 mr-2" />
                Siparişlerimi Görüntüle
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="flex-1">
              <Link href="/shop">
                <ShoppingBag className="h-4 w-4 mr-2" />
                Alışverişe Devam Et
              </Link>
            </Button>
            
            <Button variant="outline" asChild>
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Ana Sayfa
              </Link>
            </Button>
          </div>
          
          {/* Additional Info */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">
                  Sipariş Takibi
                </h4>
                <p className="text-sm text-blue-700">
                  Siparişinizin durumunu "Siparişlerim" sayfasından takip edebilirsiniz. 
                  Kargo çıkışında SMS ile bilgilendirileceksiniz.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
