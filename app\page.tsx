'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ProductCard } from '@/components/products/product-card'
import { LoadingCard } from '@/components/ui/loading-spinner'
import { useProductStore } from '@/lib/store/product-store'
import { useDataBridge } from '@/lib/hooks/use-data-bridge'
import { ShoppingBag, Users, Package, TrendingUp, Shield, Truck, Star, ArrowRight } from 'lucide-react'

export default function HomePage() {
  const { featuredProducts, isLoading, fetchFeaturedProducts } = useProductStore()
  const { isLoaded, getProducts, getPageContent, getSiteSettings } = useDataBridge()
  const [bridgeProducts, setBridgeProducts] = useState([])
  const [homeContent, setHomeContent] = useState({})
  const [siteSettings, setSiteSettings] = useState({})

  useEffect(() => {
    fetchFeaturedProducts()
  }, [fetchFeaturedProducts])

  useEffect(() => {
    if (isLoaded) {
      const products = getProducts().filter(p => p.isFeatured && p.isActive)
      setBridgeProducts(products)
      setHomeContent(getPageContent('home'))
      setSiteSettings(getSiteSettings())
    }
  }, [isLoaded, getProducts, getPageContent, getSiteSettings])

  // Use data bridge products if available, otherwise fallback to store
  const displayProducts = bridgeProducts.length > 0 ? bridgeProducts : featuredProducts

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section
        className="text-white py-20"
        style={{
          background: `linear-gradient(to right, ${siteSettings.primaryColor || '#3b82f6'}, ${siteSettings.secondaryColor || '#8b5cf6'})`
        }}
      >
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            {homeContent.heroTitle || 'E-Marketing\'e Hoş Geldiniz'}
          </h1>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            {homeContent.heroSubtitle || 'Modern, güvenli ve kullanıcı dostu e-ticaret deneyimi için doğru adrestesiniz. Binlerce ürün arasından seçim yapın ve hızlı teslimatın keyfini çıkarın.'}
          </p>
          <div className="space-x-4">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <Link href="/shop">Alışverişe Başla</Link>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Link href="/auth/register">Üye Ol</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Neden E-Marketing?</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <ShoppingBag className="w-12 h-12 mx-auto text-blue-600 mb-4" />
                <CardTitle>Geniş Ürün Yelpazesi</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Binlerce ürün arasından istediğinizi kolayca bulun ve karşılaştırın.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="w-12 h-12 mx-auto text-green-600 mb-4" />
                <CardTitle>Güvenli Alışveriş</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  SSL sertifikası ve güvenli ödeme sistemleri ile korumalı alışveriş.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Package className="w-12 h-12 mx-auto text-purple-600 mb-4" />
                <CardTitle>Hızlı Teslimat</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Siparişleriniz en kısa sürede kapınıza teslim edilir.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <TrendingUp className="w-12 h-12 mx-auto text-orange-600 mb-4" />
                <CardTitle>En İyi Fiyatlar</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Rekabetçi fiyatlar ve özel indirimlerle tasarruf edin.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {homeContent.featuredTitle || 'Öne Çıkan Ürünler'}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              En popüler ve kaliteli ürünlerimizi keşfedin
            </p>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <LoadingCard key={i} />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {displayProducts.slice(0, 4).map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}

          {displayProducts.length > 4 && (
            <div className="text-center mt-8">
              <Button asChild>
                <Link href="/shop">
                  Tüm Ürünleri Görüntüle
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Hemen Başlayın!</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Ücretsiz üyelik oluşturun ve özel indirimlerden yararlanın.
          </p>
          <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
            <Link href="/auth/register">Ücretsiz Üye Ol</Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">E-Marketing</h3>
              <p className="text-gray-400">
                Modern e-ticaret deneyimi için güvenilir adresiniz.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Hızlı Linkler</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/shop" className="hover:text-white">Ürünler</Link></li>
                <li><Link href="/categories" className="hover:text-white">Kategoriler</Link></li>
                <li><Link href="/about" className="hover:text-white">Hakkımızda</Link></li>
                <li><Link href="/contact" className="hover:text-white">İletişim</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Müşteri Hizmetleri</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/help" className="hover:text-white">Yardım</Link></li>
                <li><Link href="/returns" className="hover:text-white">İade & Değişim</Link></li>
                <li><Link href="/shipping" className="hover:text-white">Kargo Bilgileri</Link></li>
                <li><Link href="/faq" className="hover:text-white">SSS</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">İletişim</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Email: <EMAIL></li>
                <li>Telefon: +90 (212) 123 45 67</li>
                <li>Adres: İstanbul, Türkiye</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 E-Marketing. Tüm hakları saklıdır.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
