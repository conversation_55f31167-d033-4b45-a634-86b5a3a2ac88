import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from 'react-hot-toast'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { AuthProvider } from '@/components/providers/auth-provider'
import { DataBridgeProvider } from '@/components/providers/data-bridge-provider'
import Script from 'next/script'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'E-Marketing - Modern E-Ticaret Sitesi',
  description: 'Modern ve güvenli e-ticaret deneyimi için E-Marketing',
  keywords: 'e-ticaret, online alışveriş, güvenli ödeme, hızlı teslimat',
  authors: [{ name: 'E-Marketing Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'E-Marketing - Modern E-Ticaret Sitesi',
    description: 'Modern ve güvenli e-ticaret deneyimi için E-Marketing',
    type: 'website',
    locale: 'tr_TR',
    siteName: 'E-Marketing',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'E-Marketing - Modern E-Ticaret Sitesi',
    description: 'Modern ve güvenli e-ticaret deneyimi için E-Marketing',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr">
      <body className={inter.className}>
        <Script src="/lib/data-bridge.js" strategy="beforeInteractive" />
        <AuthProvider>
          <DataBridgeProvider>
            <div className="min-h-screen bg-background flex flex-col">
              <Header />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </DataBridgeProvider>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--card))',
                color: 'hsl(var(--card-foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </AuthProvider>
      </body>
    </html>
  )
}
