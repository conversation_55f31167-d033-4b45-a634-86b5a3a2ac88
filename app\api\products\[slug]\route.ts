import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const product = await prisma.product.findUnique({
      where: {
        slug: params.slug,
        isActive: true,
        status: 'ACTIVE',
      },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' }
        },
        reviews: {
          include: {
            user: {
              select: {
                name: true,
                avatar: true,
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })
    
    if (!product) {
      return NextResponse.json(
        { success: false, error: '<PERSON>r<PERSON>n bulunamadı' },
        { status: 404 }
      )
    }
    
    // Calculate average rating
    const averageRating = product.reviews.length > 0 
      ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
      : 0
    
    const transformedProduct = {
      ...product,
      averageRating,
      reviewCount: product.reviews.length,
    }
    
    return NextResponse.json({
      success: true,
      data: transformedProduct
    })
    
  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json(
      { success: false, error: 'Ürün yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const body = await request.json()
    
    const product = await prisma.product.update({
      where: { slug: params.slug },
      data: {
        name: body.name,
        slug: body.slug,
        description: body.description,
        price: body.price,
        comparePrice: body.comparePrice,
        sku: body.sku,
        stock: body.stock,
        categoryId: body.categoryId,
        isActive: body.isActive,
        isFeatured: body.isFeatured,
        weight: body.weight,
        dimensions: body.dimensions,
        status: body.status,
        updatedAt: new Date(),
      },
      include: {
        category: true,
        images: true,
      }
    })
    
    return NextResponse.json({
      success: true,
      data: product,
      message: 'Ürün başarıyla güncellendi'
    })
    
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { success: false, error: 'Ürün güncellenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await prisma.product.update({
      where: { slug: params.slug },
      data: {
        isActive: false,
        status: 'ARCHIVED',
        updatedAt: new Date(),
      }
    })
    
    return NextResponse.json({
      success: true,
      message: 'Ürün başarıyla silindi'
    })
    
  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      { success: false, error: 'Ürün silinirken hata oluştu' },
      { status: 500 }
    )
  }
}
