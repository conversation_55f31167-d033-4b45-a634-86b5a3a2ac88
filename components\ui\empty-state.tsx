import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { LucideIcon } from 'lucide-react'

interface EmptyStateProps {
  icon: LucideIcon
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className = ''
}: EmptyStateProps) {
  return (
    <Card className={className}>
      <CardContent className="flex flex-col items-center justify-center text-center py-12 px-6">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6">
          <Icon className="h-8 w-8 text-gray-400" />
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {title}
        </h3>
        
        <p className="text-gray-600 mb-6 max-w-md">
          {description}
        </p>
        
        {action && (
          <Button onClick={action.onClick}>
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

// Predefined empty states for common scenarios
export function EmptyProductsState({ onAddProduct }: { onAddProduct?: () => void }) {
  return (
    <EmptyState
      icon={Package}
      title="Henüz ürün yok"
      description="İlk ürününüzü ekleyerek başlayın ve müşterilerinizle buluşturun."
      action={onAddProduct ? {
        label: "İlk Ürünü Ekle",
        onClick: onAddProduct
      } : undefined}
    />
  )
}

export function EmptyOrdersState() {
  return (
    <EmptyState
      icon={ShoppingCart}
      title="Henüz sipariş yok"
      description="Müşterilerinizden gelen siparişler burada görünecek."
    />
  )
}

export function EmptyCartState({ onStartShopping }: { onStartShopping?: () => void }) {
  return (
    <EmptyState
      icon={ShoppingBag}
      title="Sepetiniz boş"
      description="Alışverişe başlamak için ürünlerimizi keşfedin."
      action={onStartShopping ? {
        label: "Alışverişe Başla",
        onClick: onStartShopping
      } : undefined}
    />
  )
}

export function EmptySearchState({ searchQuery }: { searchQuery: string }) {
  return (
    <EmptyState
      icon={Search}
      title="Sonuç bulunamadı"
      description={`"${searchQuery}" aramanız için sonuç bulunamadı. Farklı anahtar kelimeler deneyin.`}
    />
  )
}

export function EmptyFavoritesState({ onStartShopping }: { onStartShopping?: () => void }) {
  return (
    <EmptyState
      icon={Heart}
      title="Favorileriniz boş"
      description="Beğendiğiniz ürünleri favorilerinize ekleyerek daha sonra kolayca bulabilirsiniz."
      action={onStartShopping ? {
        label: "Ürünleri Keşfet",
        onClick: onStartShopping
      } : undefined}
    />
  )
}

// Import icons
import { Package, ShoppingCart, ShoppingBag, Search, Heart } from 'lucide-react'
