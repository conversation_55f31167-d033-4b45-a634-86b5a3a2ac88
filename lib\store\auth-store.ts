import { create } from 'zustand'
import { User, Profile, Address } from '@/types'
import { createClient } from '@/lib/supabase/client'

interface AuthState {
  // User data
  user: User | null
  profile: Profile | null
  isAuthenticated: boolean
  
  // Loading states
  isLoading: boolean
  isSigningIn: boolean
  isSigningUp: boolean
  isSigningOut: boolean
  
  // Actions
  setUser: (user: User | null) => void
  setProfile: (profile: Profile | null) => void
  setLoading: (loading: boolean) => void
  
  // Auth actions
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signUp: (email: string, password: string, name: string, phone?: string) => Promise<{ success: boolean; error?: string }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>
  updateProfile: (data: Partial<Profile>) => Promise<{ success: boolean; error?: string }>
  
  // Initialize auth state
  initialize: () => Promise<void>
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  profile: null,
  isAuthenticated: false,
  isLoading: true,
  isSigningIn: false,
  isSigningUp: false,
  isSigningOut: false,
  
  // Setters
  setUser: (user) => set({ 
    user, 
    isAuthenticated: !!user,
    isLoading: false 
  }),
  
  setProfile: (profile) => set({ profile }),
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  // Auth actions
  signIn: async (email, password) => {
    set({ isSigningIn: true })
    
    try {
      const supabase = createClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      if (data.user) {
        // Fetch user profile
        const { data: userData } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single()
        
        if (userData) {
          get().setUser(userData)
          
          // Fetch profile
          const { data: profileData } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', data.user.id)
            .single()
          
          if (profileData) {
            get().setProfile(profileData)
          }
        }
      }
      
      return { success: true }
    } catch (error) {
      console.error('Sign in error:', error)
      return { success: false, error: 'Giriş yapılırken hata oluştu' }
    } finally {
      set({ isSigningIn: false })
    }
  },
  
  signUp: async (email, password, name, phone) => {
    set({ isSigningUp: true })
    
    try {
      const supabase = createClient()
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            phone,
          }
        }
      })
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      if (data.user) {
        // Create user record
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email,
            name,
            phone,
            role: 'USER',
          })
        
        if (userError) {
          console.error('User creation error:', userError)
        }
        
        // Create profile record
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            user_id: data.user.id,
          })
        
        if (profileError) {
          console.error('Profile creation error:', profileError)
        }
      }
      
      return { success: true }
    } catch (error) {
      console.error('Sign up error:', error)
      return { success: false, error: 'Kayıt olurken hata oluştu' }
    } finally {
      set({ isSigningUp: false })
    }
  },
  
  signOut: async () => {
    set({ isSigningOut: true })
    
    try {
      const supabase = createClient()
      await supabase.auth.signOut()
      
      set({
        user: null,
        profile: null,
        isAuthenticated: false,
      })
    } catch (error) {
      console.error('Sign out error:', error)
    } finally {
      set({ isSigningOut: false })
    }
  },
  
  resetPassword: async (email) => {
    try {
      const supabase = createClient()
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      return { success: true }
    } catch (error) {
      console.error('Reset password error:', error)
      return { success: false, error: 'Şifre sıfırlama e-postası gönderilirken hata oluştu' }
    }
  },
  
  updateProfile: async (data) => {
    try {
      const { user } = get()
      if (!user) {
        return { success: false, error: 'Kullanıcı oturumu bulunamadı' }
      }
      
      const supabase = createClient()
      const { error } = await supabase
        .from('profiles')
        .update(data)
        .eq('user_id', user.id)
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      // Update local state
      const currentProfile = get().profile
      if (currentProfile) {
        set({ profile: { ...currentProfile, ...data } })
      }
      
      return { success: true }
    } catch (error) {
      console.error('Update profile error:', error)
      return { success: false, error: 'Profil güncellenirken hata oluştu' }
    }
  },
  
  initialize: async () => {
    try {
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        // Fetch user data
        const { data: userData } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()
        
        if (userData) {
          get().setUser(userData)
          
          // Fetch profile
          const { data: profileData } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', session.user.id)
            .single()
          
          if (profileData) {
            get().setProfile(profileData)
          }
        }
      } else {
        set({ isLoading: false })
      }
      
      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        if (event === 'SIGNED_OUT' || !session) {
          set({
            user: null,
            profile: null,
            isAuthenticated: false,
            isLoading: false,
          })
        }
      })
    } catch (error) {
      console.error('Auth initialization error:', error)
      set({ isLoading: false })
    }
  },
}))
