import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    const { data: addresses, error } = await supabase
      .from('addresses')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('is_default', { ascending: false })
      .order('created_at', { ascending: false })
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: addresses || []
    })
    
  } catch (error) {
    console.error('Error fetching addresses:', error)
    return NextResponse.json(
      { success: false, error: '<PERSON><PERSON>ler yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      firstName,
      lastName,
      phone,
      address,
      district,
      city,
      postalCode,
      isDefault
    } = body
    
    // Validate required fields
    if (!title || !firstName || !lastName || !phone || !address || !district || !city || !postalCode) {
      return NextResponse.json(
        { success: false, error: 'Tüm alanlar zorunludur' },
        { status: 400 }
      )
    }
    
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // If this is set as default, unset other default addresses
    if (isDefault) {
      await supabase
        .from('addresses')
        .update({ is_default: false })
        .eq('user_id', user.id)
    }
    
    const { data: address, error } = await supabase
      .from('addresses')
      .insert({
        user_id: user.id,
        title,
        first_name: firstName,
        last_name: lastName,
        phone,
        address,
        district,
        city,
        postal_code: postalCode,
        is_default: isDefault || false,
        is_active: true,
      })
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: address,
      message: 'Adres başarıyla eklendi'
    })
    
  } catch (error) {
    console.error('Error creating address:', error)
    return NextResponse.json(
      { success: false, error: 'Adres eklenirken hata oluştu' },
      { status: 500 }
    )
  }
}
