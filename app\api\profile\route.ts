import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Fetch user data
    const { data: userData, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    // Fetch profile data
    const { data: profileData } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()
    
    return NextResponse.json({
      success: true,
      data: {
        user: userData,
        profile: profileData
      }
    })
    
  } catch (error) {
    console.error('Error fetching profile:', error)
    return NextResponse.json(
      { success: false, error: 'Profil yükle<PERSON>ken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    const { name, phone, dateOfBirth, gender, preferences } = body
    
    // Update user data
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        name,
        phone,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
    
    if (userUpdateError) {
      return NextResponse.json(
        { success: false, error: userUpdateError.message },
        { status: 500 }
      )
    }
    
    // Update or create profile
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('user_id', user.id)
      .single()
    
    if (existingProfile) {
      // Update existing profile
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({
          date_of_birth: dateOfBirth,
          gender,
          preferences,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
      
      if (profileUpdateError) {
        return NextResponse.json(
          { success: false, error: profileUpdateError.message },
          { status: 500 }
        )
      }
    } else {
      // Create new profile
      const { error: profileCreateError } = await supabase
        .from('profiles')
        .insert({
          user_id: user.id,
          date_of_birth: dateOfBirth,
          gender,
          preferences
        })
      
      if (profileCreateError) {
        return NextResponse.json(
          { success: false, error: profileCreateError.message },
          { status: 500 }
        )
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Profil başarıyla güncellendi'
    })
    
  } catch (error) {
    console.error('Error updating profile:', error)
    return NextResponse.json(
      { success: false, error: 'Profil güncellenirken hata oluştu' },
      { status: 500 }
    )
  }
}
