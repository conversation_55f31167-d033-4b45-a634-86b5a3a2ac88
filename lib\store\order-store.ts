import { create } from 'zustand'
import { Order, Address, OrderFilters, PaginatedResponse } from '@/types'
import { createClient } from '@/lib/supabase/client'

interface OrderState {
  // Orders data
  orders: Order[]
  currentOrder: Order | null
  
  // Filters
  filters: OrderFilters
  
  // Loading states
  isLoading: boolean
  isCreatingOrder: boolean
  isUpdatingOrder: boolean
  
  // Pagination
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  
  // Actions
  setOrders: (response: PaginatedResponse<Order>) => void
  setCurrentOrder: (order: Order | null) => void
  setFilters: (filters: Partial<OrderFilters>) => void
  setLoading: (loading: boolean) => void
  
  // Order actions
  createOrder: (orderData: {
    items: { productId: string; quantity: number; price: number }[]
    addressId: string
    paymentMethod: string
    notes?: string
  }) => Promise<{ success: boolean; order?: Order; error?: string }>
  
  updateOrderStatus: (orderId: string, status: string) => Promise<{ success: boolean; error?: string }>
  
  cancelOrder: (orderId: string, reason?: string) => Promise<{ success: boolean; error?: string }>
  
  // Fetch actions
  fetchOrders: () => Promise<void>
  fetchOrder: (orderId: string) => Promise<void>
  
  // Clear actions
  clearFilters: () => void
}

const initialFilters: OrderFilters = {
  page: 1,
  limit: 10,
}

const initialPagination = {
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrev: false,
}

export const useOrderStore = create<OrderState>((set, get) => ({
  // Initial state
  orders: [],
  currentOrder: null,
  filters: initialFilters,
  isLoading: false,
  isCreatingOrder: false,
  isUpdatingOrder: false,
  pagination: initialPagination,
  
  // Setters
  setOrders: (response) => set({
    orders: response.data,
    pagination: response.pagination,
  }),
  
  setCurrentOrder: (order) => set({ currentOrder: order }),
  
  setFilters: (newFilters) => set((state) => ({
    filters: { ...state.filters, ...newFilters, page: 1 }
  })),
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  // Order actions
  createOrder: async (orderData) => {
    set({ isCreatingOrder: true })
    
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        return { success: false, error: 'Kullanıcı oturumu bulunamadı' }
      }
      
      // Generate order number
      const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`
      
      // Calculate totals
      const totalAmount = orderData.items.reduce((sum, item) => sum + (item.quantity * item.price), 0)
      const shippingCost = totalAmount >= 150 ? 0 : 29.99
      const taxAmount = totalAmount * 0.18 // 18% KDV
      
      // Create order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: user.id,
          order_number: orderNumber,
          address_id: orderData.addressId,
          total_amount: totalAmount,
          shipping_cost: shippingCost,
          tax_amount: taxAmount,
          payment_method: orderData.paymentMethod,
          notes: orderData.notes,
          status: 'PENDING',
          payment_status: 'PENDING',
        })
        .select(`
          *,
          address:addresses(*),
          order_items:order_items(
            *,
            product:products(*)
          )
        `)
        .single()
      
      if (orderError) {
        return { success: false, error: orderError.message }
      }
      
      // Create order items
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        product_id: item.productId,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price,
      }))
      
      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems)
      
      if (itemsError) {
        // Rollback order creation
        await supabase.from('orders').delete().eq('id', order.id)
        return { success: false, error: itemsError.message }
      }
      
      // Update product stock
      for (const item of orderData.items) {
        await supabase.rpc('decrease_product_stock', {
          product_id: item.productId,
          quantity: item.quantity
        })
      }
      
      set({ currentOrder: order })
      return { success: true, order }
      
    } catch (error) {
      console.error('Create order error:', error)
      return { success: false, error: 'Sipariş oluşturulurken hata oluştu' }
    } finally {
      set({ isCreatingOrder: false })
    }
  },
  
  updateOrderStatus: async (orderId, status) => {
    set({ isUpdatingOrder: true })
    
    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('orders')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      // Update local state
      const { orders, currentOrder } = get()
      
      set({
        orders: orders.map(order => 
          order.id === orderId 
            ? { ...order, status: status as any, updatedAt: new Date() }
            : order
        ),
        currentOrder: currentOrder?.id === orderId 
          ? { ...currentOrder, status: status as any, updatedAt: new Date() }
          : currentOrder
      })
      
      return { success: true }
      
    } catch (error) {
      console.error('Update order status error:', error)
      return { success: false, error: 'Sipariş durumu güncellenirken hata oluştu' }
    } finally {
      set({ isUpdatingOrder: false })
    }
  },
  
  cancelOrder: async (orderId, reason) => {
    set({ isUpdatingOrder: true })
    
    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('orders')
        .update({ 
          status: 'CANCELLED',
          notes: reason ? `İptal nedeni: ${reason}` : undefined,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      // Restore product stock
      const { data: orderItems } = await supabase
        .from('order_items')
        .select('product_id, quantity')
        .eq('order_id', orderId)
      
      if (orderItems) {
        for (const item of orderItems) {
          await supabase.rpc('increase_product_stock', {
            product_id: item.product_id,
            quantity: item.quantity
          })
        }
      }
      
      // Update local state
      const { orders, currentOrder } = get()
      
      set({
        orders: orders.map(order => 
          order.id === orderId 
            ? { ...order, status: 'CANCELLED', updatedAt: new Date() }
            : order
        ),
        currentOrder: currentOrder?.id === orderId 
          ? { ...currentOrder, status: 'CANCELLED', updatedAt: new Date() }
          : currentOrder
      })
      
      return { success: true }
      
    } catch (error) {
      console.error('Cancel order error:', error)
      return { success: false, error: 'Sipariş iptal edilirken hata oluştu' }
    } finally {
      set({ isUpdatingOrder: false })
    }
  },
  
  fetchOrders: async () => {
    const { filters } = get()
    set({ isLoading: true })
    
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        set({ isLoading: false })
        return
      }
      
      const skip = (filters.page! - 1) * filters.limit!
      
      // Build where clause
      let query = supabase
        .from('orders')
        .select(`
          *,
          address:addresses(*),
          order_items:order_items(
            *,
            product:products(
              *,
              images:product_images(*)
            )
          ),
          shipping_info:shipping_info(*)
        `, { count: 'exact' })
        .eq('user_id', user.id)
      
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      
      if (filters.paymentStatus) {
        query = query.eq('payment_status', filters.paymentStatus)
      }
      
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }
      
      if (filters.search) {
        query = query.or(`order_number.ilike.%${filters.search}%`)
      }
      
      const { data: orders, count, error } = await query
        .order('created_at', { ascending: false })
        .range(skip, skip + filters.limit! - 1)
      
      if (error) {
        console.error('Fetch orders error:', error)
        return
      }
      
      // Calculate pagination
      const total = count || 0
      const totalPages = Math.ceil(total / filters.limit!)
      const hasNext = filters.page! < totalPages
      const hasPrev = filters.page! > 1
      
      get().setOrders({
        data: orders || [],
        pagination: {
          page: filters.page!,
          limit: filters.limit!,
          total,
          totalPages,
          hasNext,
          hasPrev,
        }
      })
      
    } catch (error) {
      console.error('Fetch orders error:', error)
    } finally {
      set({ isLoading: false })
    }
  },
  
  fetchOrder: async (orderId) => {
    set({ isLoading: true })
    
    try {
      const supabase = createClient()
      const { data: order, error } = await supabase
        .from('orders')
        .select(`
          *,
          address:addresses(*),
          order_items:order_items(
            *,
            product:products(
              *,
              category:categories(*),
              images:product_images(*)
            )
          ),
          shipping_info:shipping_info(*)
        `)
        .eq('id', orderId)
        .single()
      
      if (error) {
        console.error('Fetch order error:', error)
        return
      }
      
      set({ currentOrder: order })
      
    } catch (error) {
      console.error('Fetch order error:', error)
    } finally {
      set({ isLoading: false })
    }
  },
  
  clearFilters: () => set({ filters: initialFilters }),
}))
