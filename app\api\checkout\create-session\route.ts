import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { createCheckoutSession, formatAmountForStripe } from '@/lib/stripe/config'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { items, addressId, notes } = body
    
    // Validate request
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Sepet öğeleri gereklidir' },
        { status: 400 }
      )
    }
    
    if (!addressId) {
      return NextResponse.json(
        { success: false, error: 'Teslimat adresi gereklidir' },
        { status: 400 }
      )
    }
    
    // Get user
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: '<PERSON><PERSON><PERSON><PERSON><PERSON> oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Fetch user data
    const { data: userData } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı bilgileri bulunamadı' },
        { status: 404 }
      )
    }
    
    // Fetch address
    const { data: address } = await supabase
      .from('addresses')
      .select('*')
      .eq('id', addressId)
      .eq('user_id', user.id)
      .single()
    
    if (!address) {
      return NextResponse.json(
        { success: false, error: 'Adres bulunamadı' },
        { status: 404 }
      )
    }
    
    // Fetch products and validate
    const productIds = items.map((item: any) => item.productId)
    const { data: products } = await supabase
      .from('products')
      .select(`
        *,
        images:product_images(*)
      `)
      .in('id', productIds)
      .eq('is_active', true)
      .eq('status', 'ACTIVE')
    
    if (!products || products.length !== items.length) {
      return NextResponse.json(
        { success: false, error: 'Bazı ürünler bulunamadı veya aktif değil' },
        { status: 400 }
      )
    }
    
    // Validate stock and create line items
    const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = []
    let totalAmount = 0
    
    for (const item of items) {
      const product = products.find(p => p.id === item.productId)
      if (!product) {
        return NextResponse.json(
          { success: false, error: `Ürün bulunamadı: ${item.productId}` },
          { status: 400 }
        )
      }
      
      if (product.stock < item.quantity) {
        return NextResponse.json(
          { success: false, error: `${product.name} için yeterli stok yok` },
          { status: 400 }
        )
      }
      
      const itemTotal = product.price * item.quantity
      totalAmount += itemTotal
      
      lineItems.push({
        price_data: {
          currency: 'try',
          product_data: {
            name: product.name,
            description: product.description || undefined,
            images: product.images?.[0]?.url ? [product.images[0].url] : undefined,
            metadata: {
              product_id: product.id,
              sku: product.sku || '',
            },
          },
          unit_amount: formatAmountForStripe(product.price, 'try'),
        },
        quantity: item.quantity,
      })
    }
    
    // Add shipping if needed
    const shippingCost = totalAmount >= 150 ? 0 : 29.99
    if (shippingCost > 0) {
      lineItems.push({
        price_data: {
          currency: 'try',
          product_data: {
            name: 'Kargo Ücreti',
            description: '150₺ altı siparişler için kargo ücreti',
          },
          unit_amount: formatAmountForStripe(shippingCost, 'try'),
        },
        quantity: 1,
      })
    }
    
    // Create checkout session
    const session = await createCheckoutSession(
      lineItems,
      {
        user_id: user.id,
        address_id: addressId,
        notes: notes || '',
        items: JSON.stringify(items),
      },
      `${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      `${process.env.NEXT_PUBLIC_APP_URL}/checkout/cancel`
    )
    
    return NextResponse.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url,
      }
    })
    
  } catch (error) {
    console.error('Create checkout session error:', error)
    return NextResponse.json(
      { success: false, error: 'Ödeme oturumu oluşturulurken hata oluştu' },
      { status: 500 }
    )
  }
}
