import { create } from 'zustand'
import { Product, Category, ProductFilters, PaginatedResponse } from '@/types'

interface ProductState {
  // Products
  products: Product[]
  featuredProducts: Product[]
  currentProduct: Product | null
  
  // Categories
  categories: Category[]
  currentCategory: Category | null
  
  // Filters & Search
  filters: ProductFilters
  searchQuery: string
  
  // Loading states
  isLoading: boolean
  isLoadingProduct: boolean
  isLoadingCategories: boolean
  
  // Pagination
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  
  // Actions
  setProducts: (response: PaginatedResponse<Product>) => void
  setFeaturedProducts: (products: Product[]) => void
  setCurrentProduct: (product: Product | null) => void
  setCategories: (categories: Category[]) => void
  setCurrentCategory: (category: Category | null) => void
  setFilters: (filters: Partial<ProductFilters>) => void
  setSearchQuery: (query: string) => void
  setLoading: (loading: boolean) => void
  setLoadingProduct: (loading: boolean) => void
  setLoadingCategories: (loading: boolean) => void
  clearFilters: () => void
  
  // API Actions
  fetchProducts: () => Promise<void>
  fetchFeaturedProducts: () => Promise<void>
  fetchProduct: (slug: string) => Promise<void>
  fetchCategories: () => Promise<void>
  searchProducts: (query: string) => Promise<void>
}

const initialFilters: ProductFilters = {
  page: 1,
  limit: 12,
  sortBy: 'createdAt',
  sortOrder: 'desc',
}

const initialPagination = {
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrev: false,
}

export const useProductStore = create<ProductState>((set, get) => ({
  // Initial state
  products: [],
  featuredProducts: [],
  currentProduct: null,
  categories: [],
  currentCategory: null,
  filters: initialFilters,
  searchQuery: '',
  isLoading: false,
  isLoadingProduct: false,
  isLoadingCategories: false,
  pagination: initialPagination,
  
  // Setters
  setProducts: (response) => set({
    products: response.data,
    pagination: response.pagination,
  }),
  
  setFeaturedProducts: (products) => set({ featuredProducts: products }),
  
  setCurrentProduct: (product) => set({ currentProduct: product }),
  
  setCategories: (categories) => set({ categories }),
  
  setCurrentCategory: (category) => set({ currentCategory: category }),
  
  setFilters: (newFilters) => set((state) => ({
    filters: { ...state.filters, ...newFilters, page: 1 }
  })),
  
  setSearchQuery: (query) => set({ searchQuery: query }),
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  setLoadingProduct: (loading) => set({ isLoadingProduct: loading }),
  
  setLoadingCategories: (loading) => set({ isLoadingCategories: loading }),
  
  clearFilters: () => set({ filters: initialFilters, searchQuery: '' }),
  
  // API Actions
  fetchProducts: async () => {
    const { filters } = get()
    set({ isLoading: true })
    
    try {
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString())
        }
      })
      
      const response = await fetch(`/api/products?${params}`)
      const data = await response.json()
      
      if (data.success) {
        get().setProducts(data.data)
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      set({ isLoading: false })
    }
  },
  
  fetchFeaturedProducts: async () => {
    try {
      const response = await fetch('/api/products/featured')
      const data = await response.json()
      
      if (data.success) {
        set({ featuredProducts: data.data })
      }
    } catch (error) {
      console.error('Error fetching featured products:', error)
    }
  },
  
  fetchProduct: async (slug) => {
    set({ isLoadingProduct: true })
    
    try {
      const response = await fetch(`/api/products/${slug}`)
      const data = await response.json()
      
      if (data.success) {
        set({ currentProduct: data.data })
      }
    } catch (error) {
      console.error('Error fetching product:', error)
    } finally {
      set({ isLoadingProduct: false })
    }
  },
  
  fetchCategories: async () => {
    set({ isLoadingCategories: true })
    
    try {
      const response = await fetch('/api/categories')
      const data = await response.json()
      
      if (data.success) {
        set({ categories: data.data })
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      set({ isLoadingCategories: false })
    }
  },
  
  searchProducts: async (query) => {
    set({ searchQuery: query, isLoading: true })
    
    try {
      const response = await fetch(`/api/products/search?q=${encodeURIComponent(query)}`)
      const data = await response.json()
      
      if (data.success) {
        get().setProducts(data.data)
      }
    } catch (error) {
      console.error('Error searching products:', error)
    } finally {
      set({ isLoading: false })
    }
  },
}))
