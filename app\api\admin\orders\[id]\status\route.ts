import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Yetkisiz erişim' },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    const { status, notes } = body
    
    if (!status) {
      return NextResponse.json(
        { success: false, error: 'Durum bilgisi gereklidir' },
        { status: 400 }
      )
    }
    
    // Validate status
    const validStatuses = ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Geçersiz durum' },
        { status: 400 }
      )
    }
    
    // Get current order
    const { data: currentOrder, error: fetchError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', params.id)
      .single()
    
    if (fetchError || !currentOrder) {
      return NextResponse.json(
        { success: false, error: 'Sipariş bulunamadı' },
        { status: 404 }
      )
    }
    
    // Update order status
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    }
    
    if (notes) {
      updateData.notes = notes
    }
    
    // Set shipped date if status is SHIPPED
    if (status === 'SHIPPED' && currentOrder.status !== 'SHIPPED') {
      updateData.shipped_at = new Date().toISOString()
    }
    
    // Set delivered date if status is DELIVERED
    if (status === 'DELIVERED' && currentOrder.status !== 'DELIVERED') {
      updateData.delivered_at = new Date().toISOString()
    }
    
    const { data: updatedOrder, error: updateError } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        user:users(name, email),
        order_items:order_items(
          *,
          product:products(name)
        )
      `)
      .single()
    
    if (updateError) {
      return NextResponse.json(
        { success: false, error: updateError.message },
        { status: 500 }
      )
    }
    
    // Create status history record
    await supabase
      .from('order_status_history')
      .insert({
        order_id: params.id,
        status,
        notes,
        changed_by: user.id,
        changed_at: new Date().toISOString()
      })
    
    // TODO: Send notification email to customer
    // TODO: Send SMS notification if phone number is available
    
    return NextResponse.json({
      success: true,
      data: updatedOrder,
      message: 'Sipariş durumu başarıyla güncellendi'
    })
    
  } catch (error) {
    console.error('Error updating order status:', error)
    return NextResponse.json(
      { success: false, error: 'Sipariş durumu güncellenirken hata oluştu' },
      { status: 500 }
    )
  }
}
