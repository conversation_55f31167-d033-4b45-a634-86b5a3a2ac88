# 🧪 E-Marketing Kapsamlı Test Rehberi

Bu rehber, E-Marketing e-ticaret platformunun tüm özelliklerini test etmek için hazırlanmıştır.

## 🚀 Hızlı Başlangıç

### 1. Projeyi Başlatın
```bash
npm run dev
```

### 2. Tarayıcıda Açın
http://localhost:3000

## 📋 Test Senaryoları

### ✅ 1. Ana <PERSON>fa (/)
- [ ] Sayfa yükleniyor
- [ ] Header navigation çalışıyor
- [ ] Hero section görüntüleniyor
- [ ] Öne çıkan ürünler listeleniyor
- [ ] Footer görüntüleniyor
- [ ] Responsive tasarım çalışıyor

### ✅ 2. Authentication Sayfaları

#### 2.1 Giriş Sayfası (/auth/login)
- [ ] Form görüntüleniyor
- [ ] E-posta validasyonu çalışıyor
- [ ] Şifre validasyonu çalışıyor
- [ ] "Şifremi Unuttum" linki çalışıyor
- [ ] "Kayıt Ol" linki yönlendiriyor
- [ ] G<PERSON>ş işlemi çalışıyor

#### 2.2 Kayıt Sayfası (/auth/register)
- [ ] Form görüntüleniyor
- [ ] Tüm validasyonlar çalışıyor
- [ ] Şifre onayı kontrolü çalışıyor
- [ ] Kullanım koşulları checkbox'ı zorunlu
- [ ] Kayıt işlemi çalışıyor

### ✅ 3. Ürün Sayfaları

#### 3.1 Kategoriler (/categories)
- [ ] Kategori listesi görüntüleniyor
- [ ] Kategori kartları tıklanabilir
- [ ] Arama fonksiyonu çalışıyor
- [ ] Filtreleme çalışıyor

#### 3.2 Ürün Listesi (/shop)
- [ ] Ürünler listeleniyor
- [ ] Arama çalışıyor
- [ ] Filtreleme çalışıyor
- [ ] Sıralama çalışıyor
- [ ] Sayfalama çalışıyor
- [ ] Grid/List görünüm değişimi

#### 3.3 Ürün Detayı (/products/[slug])
- [ ] Ürün bilgileri görüntüleniyor
- [ ] Resim galerisi çalışıyor
- [ ] Miktar seçimi çalışıyor
- [ ] "Sepete Ekle" butonu çalışıyor
- [ ] Favorilere ekleme çalışıyor
- [ ] Paylaşım fonksiyonu çalışıyor

### ✅ 4. Sepet ve Ödeme

#### 4.1 Sepet (/cart)
- [ ] Sepet ürünleri görüntüleniyor
- [ ] Miktar değiştirme çalışıyor
- [ ] Ürün silme çalışıyor
- [ ] Kupon kodu uygulanıyor
- [ ] Toplam hesaplama doğru
- [ ] "Ödemeye Geç" yönlendiriyor

#### 4.2 Ödeme (/checkout)
- [ ] Adres formu çalışıyor
- [ ] Ödeme yöntemi seçimi çalışıyor
- [ ] Kredi kartı formu görüntüleniyor
- [ ] Form validasyonları çalışıyor
- [ ] Sipariş özeti doğru
- [ ] Sipariş tamamlama çalışıyor

#### 4.3 Ödeme Başarılı (/checkout/success)
- [ ] Başarı mesajı görüntüleniyor
- [ ] Sipariş numarası gösteriliyor
- [ ] Teslimat bilgileri görüntüleniyor
- [ ] İletişim bilgileri mevcut

### ✅ 5. Kullanıcı Profili

#### 5.1 Profil (/profile)
- [ ] Kullanıcı bilgileri görüntüleniyor
- [ ] İstatistikler gösteriliyor
- [ ] Hızlı işlem linkleri çalışıyor
- [ ] Profil düzenleme linki çalışıyor

#### 5.2 Siparişler (/orders)
- [ ] Sipariş listesi görüntüleniyor
- [ ] Sipariş durumları gösteriliyor
- [ ] Sipariş detayları görüntüleniyor
- [ ] Boş liste durumu gösteriliyor

### ✅ 6. Kurumsal Sayfalar

#### 6.1 Hakkımızda (/about)
- [ ] Şirket bilgileri görüntüleniyor
- [ ] Misyon, vizyon, değerler mevcut
- [ ] Ekip bilgileri gösteriliyor
- [ ] İstatistikler görüntüleniyor

#### 6.2 İletişim (/contact)
- [ ] İletişim formu çalışıyor
- [ ] Form validasyonları çalışıyor
- [ ] İletişim bilgileri görüntüleniyor
- [ ] Harita placeholder'ı mevcut

#### 6.3 Gizlilik Politikası (/privacy)
- [ ] KVKK bilgileri mevcut
- [ ] Veri toplama açıklamaları var
- [ ] Kullanıcı hakları listeleniyor
- [ ] İletişim bilgileri mevcut

#### 6.4 Kullanım Şartları (/terms)
- [ ] Yasal şartlar listeleniyor
- [ ] Alışveriş koşulları mevcut
- [ ] Ödeme koşulları açıklanmış
- [ ] İade koşulları belirtilmiş

#### 6.5 İade Politikası (/returns)
- [ ] İade koşulları açıklanmış
- [ ] İade süreci adımları mevcut
- [ ] İade masrafları belirtilmiş
- [ ] İletişim bilgileri var

#### 6.6 Kargo Bilgileri (/shipping)
- [ ] Kargo ücretleri listeleniyor
- [ ] Teslimat süreleri belirtilmiş
- [ ] Kargo firmaları listeleniyor
- [ ] Özel ürün teslimatı açıklanmış

#### 6.7 Sıkça Sorulan Sorular (/faq)
- [ ] Kategori filtreleme çalışıyor
- [ ] Arama fonksiyonu çalışıyor
- [ ] Accordion açılıp kapanıyor
- [ ] İletişim CTA'sı mevcut

### ✅ 7. Admin Panel

#### 7.1 Admin Setup (/admin/setup)
- [ ] Setup formu görüntüleniyor
- [ ] Admin kullanıcısı oluşturuluyor
- [ ] Yönlendirme çalışıyor

#### 7.2 Admin Dashboard (/admin)
- [ ] Dashboard yükleniyor
- [ ] İstatistikler görüntüleniyor
- [ ] Sidebar navigation çalışıyor
- [ ] Erişim kontrolü çalışıyor

### ✅ 8. Navigation ve Layout

#### 8.1 Header
- [ ] Logo ana sayfaya yönlendiriyor
- [ ] Tüm navigation linkleri çalışıyor
- [ ] Arama çubuğu çalışıyor
- [ ] Kullanıcı dropdown'u çalışıyor
- [ ] Sepet ikonu çalışıyor
- [ ] Mobile menu çalışıyor

#### 8.2 Footer
- [ ] Tüm linkler çalışıyor
- [ ] İletişim bilgileri doğru
- [ ] Sosyal medya linkleri mevcut
- [ ] Ödeme yöntemleri gösteriliyor

### ✅ 9. Responsive Tasarım
- [ ] Mobile (320px-768px) uyumlu
- [ ] Tablet (768px-1024px) uyumlu
- [ ] Desktop (1024px+) uyumlu
- [ ] Touch gestures çalışıyor

### ✅ 10. SEO ve Meta
- [ ] Tüm sayfalarda title mevcut
- [ ] Meta descriptions mevcut
- [ ] Keywords tanımlanmış
- [ ] Open Graph tags mevcut

## 🔧 Test Araçları

### Manuel Test
1. Tarayıcı geliştirici araçları
2. Responsive design mode
3. Network tab (API çağrıları)
4. Console (hata kontrolü)

### Otomatik Test (Gelecek)
```bash
# Unit testler
npm run test

# E2E testler
npm run test:e2e

# Lighthouse audit
npm run audit
```

## 🐛 Bilinen Sorunlar

### Çözülmesi Gerekenler
- [ ] Supabase bağlantısı yapılandırılmalı
- [ ] Demo veriler yüklenmelidir
- [ ] Admin kullanıcısı oluşturulmalı
- [ ] Gerçek ödeme entegrasyonu (opsiyonel)

### Geliştirme Önerileri
- [ ] Loading states iyileştirilebilir
- [ ] Error handling genişletilebilir
- [ ] Caching mekanizması eklenebilir
- [ ] PWA özellikleri eklenebilir

## 📊 Test Sonuçları

### Başarılı Testler: ___/100
### Başarısız Testler: ___/100
### Test Kapsamı: ___%

## 🚀 Deployment Öncesi Kontrol Listesi

- [ ] Tüm sayfalar çalışıyor
- [ ] API endpoints test edildi
- [ ] Database bağlantısı çalışıyor
- [ ] Environment variables ayarlandı
- [ ] Build hatası yok
- [ ] Performance optimizasyonu yapıldı
- [ ] Security kontrolleri tamamlandı

## 📞 Destek

Test sırasında sorun yaşarsanız:
- GitHub Issues: [Proje Repository]
- E-posta: <EMAIL>
- Dokümantasyon: README.md

---

**Not:** Bu test rehberi sürekli güncellenmektedir. Yeni özellikler eklendikçe test senaryoları da genişletilecektir.
