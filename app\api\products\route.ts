import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { ProductFilters } from '@/types'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const filters: ProductFilters = {
      category: searchParams.get('category') || undefined,
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      inStock: searchParams.get('inStock') === 'true',
      featured: searchParams.get('featured') === 'true',
      search: searchParams.get('search') || undefined,
      sortBy: (searchParams.get('sortBy') as 'name' | 'price' | 'createdAt') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 12,
    }

    const skip = (filters.page! - 1) * filters.limit!
    
    // Build where clause
    const where: any = {
      isActive: true,
      status: 'ACTIVE',
    }
    
    if (filters.category) {
      where.category = {
        slug: filters.category
      }
    }
    
    if (filters.minPrice || filters.maxPrice) {
      where.price = {}
      if (filters.minPrice) where.price.gte = filters.minPrice
      if (filters.maxPrice) where.price.lte = filters.maxPrice
    }
    
    if (filters.inStock) {
      where.stock = { gt: 0 }
    }
    
    if (filters.featured) {
      where.isFeatured = true
    }
    
    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { sku: { contains: filters.search, mode: 'insensitive' } },
      ]
    }
    
    // Build orderBy clause
    const orderBy: any = {}
    if (filters.sortBy === 'name') {
      orderBy.name = filters.sortOrder
    } else if (filters.sortBy === 'price') {
      orderBy.price = filters.sortOrder
    } else {
      orderBy.createdAt = filters.sortOrder
    }
    
    // Get total count
    const total = await prisma.product.count({ where })
    
    // Get products
    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' }
        },
        reviews: {
          select: {
            rating: true
          }
        }
      },
      orderBy,
      skip,
      take: filters.limit,
    })
    
    // Calculate pagination
    const totalPages = Math.ceil(total / filters.limit!)
    const hasNext = filters.page! < totalPages
    const hasPrev = filters.page! > 1
    
    // Transform products with average rating
    const transformedProducts = products.map(product => ({
      ...product,
      averageRating: product.reviews.length > 0 
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
        : 0,
      reviewCount: product.reviews.length,
      reviews: undefined, // Remove reviews from response
    }))
    
    return NextResponse.json({
      success: true,
      data: {
        data: transformedProducts,
        pagination: {
          page: filters.page,
          limit: filters.limit,
          total,
          totalPages,
          hasNext,
          hasPrev,
        }
      }
    })
    
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { success: false, error: 'Ürünler yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const product = await prisma.product.create({
      data: {
        name: body.name,
        slug: body.slug,
        description: body.description,
        price: body.price,
        comparePrice: body.comparePrice,
        sku: body.sku,
        stock: body.stock,
        categoryId: body.categoryId,
        isActive: body.isActive ?? true,
        isFeatured: body.isFeatured ?? false,
        weight: body.weight,
        dimensions: body.dimensions,
        status: body.status ?? 'ACTIVE',
      },
      include: {
        category: true,
        images: true,
      }
    })
    
    return NextResponse.json({
      success: true,
      data: product,
      message: 'Ürün başarıyla oluşturuldu'
    })
    
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { success: false, error: 'Ürün oluşturulurken hata oluştu' },
      { status: 500 }
    )
  }
}
