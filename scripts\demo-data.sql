-- Demo Data for E-Marketing Platform
-- Run this script in your Supabase SQL editor to populate demo data

-- Insert demo categories
INSERT INTO categories (id, name, slug, description, is_active, sort_order) VALUES
('cat-1', 'Elektronik', 'elektronik', 'Elektronik ürünler ve aksesuarlar', true, 1),
('cat-2', '<PERSON><PERSON>yi<PERSON>', 'giyim', '<PERSON><PERSON><PERSON><PERSON>, erkek ve çocuk giyim', true, 2),
('cat-3', 'Ev & Yaşam', 'ev-yasam', 'Ev dekorasyonu ve yaşam ürünleri', true, 3),
('cat-4', 'Spor & Outdoor', 'spor-outdoor', 'Spor malzemeleri ve outdoor ürünler', true, 4),
('cat-5', 'Kitap & Hobi', 'kitap-hobi', 'Kitaplar ve hobi malzemeleri', true, 5);

-- Insert demo products
INSERT INTO products (id, name, slug, description, price, compare_price, sku, stock, category_id, is_active, is_featured, status) VALUES
('prod-1', 'iPhone 15 Pro', 'iphone-15-pro', 'Apple iPhone 15 Pro 128GB - Titanium Blue', 54999.00, 59999.00, 'IP15P-128-TB', 25, 'cat-1', true, true, 'ACTIVE'),
('prod-2', 'Samsung Galaxy S24', 'samsung-galaxy-s24', 'Samsung Galaxy S24 256GB - Phantom Black', 42999.00, 47999.00, 'SGS24-256-PB', 30, 'cat-1', true, true, 'ACTIVE'),
('prod-3', 'MacBook Air M3', 'macbook-air-m3', 'Apple MacBook Air 13" M3 Chip 8GB RAM 256GB SSD', 42999.00, 45999.00, 'MBA-M3-256', 15, 'cat-1', true, true, 'ACTIVE'),
('prod-4', 'Nike Air Max 270', 'nike-air-max-270', 'Nike Air Max 270 Erkek Spor Ayakkabı', 3299.00, 3999.00, 'NAM270-42', 50, 'cat-4', true, false, 'ACTIVE'),
('prod-5', 'Adidas Ultraboost 22', 'adidas-ultraboost-22', 'Adidas Ultraboost 22 Koşu Ayakkabısı', 4199.00, 4799.00, 'AUB22-43', 35, 'cat-4', true, true, 'ACTIVE'),
('prod-6', 'Levi''s 501 Jeans', 'levis-501-jeans', 'Levi''s 501 Original Fit Erkek Jean', 899.00, 1199.00, 'L501-32-34', 40, 'cat-2', true, false, 'ACTIVE'),
('prod-7', 'Zara Blazer Ceket', 'zara-blazer-ceket', 'Zara Kadın Blazer Ceket - Siyah', 1299.00, 1599.00, 'ZBC-S-SYH', 20, 'cat-2', true, false, 'ACTIVE'),
('prod-8', 'IKEA BILLY Kitaplık', 'ikea-billy-kitaplik', 'IKEA BILLY Kitaplık - Beyaz 80x28x202 cm', 599.00, 799.00, 'IK-BILLY-WHT', 25, 'cat-3', true, false, 'ACTIVE'),
('prod-9', 'Philips Hue Akıllı Ampul', 'philips-hue-akilli-ampul', 'Philips Hue White and Color Ambiance E27', 899.00, 1099.00, 'PH-HUE-E27', 60, 'cat-3', true, true, 'ACTIVE'),
('prod-10', 'Sapiens Kitabı', 'sapiens-kitabi', 'Sapiens: İnsan Türünün Kısa Bir Tarihi - Yuval Noah Harari', 89.00, 119.00, 'SAP-YNH-TR', 100, 'cat-5', true, false, 'ACTIVE');

-- Insert demo product images
INSERT INTO product_images (product_id, url, alt, sort_order) VALUES
('prod-1', 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500', 'iPhone 15 Pro', 0),
('prod-2', 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500', 'Samsung Galaxy S24', 0),
('prod-3', 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500', 'MacBook Air M3', 0),
('prod-4', 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500', 'Nike Air Max 270', 0),
('prod-5', 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=500', 'Adidas Ultraboost 22', 0),
('prod-6', 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', 'Levi''s 501 Jeans', 0),
('prod-7', 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500', 'Zara Blazer Ceket', 0),
('prod-8', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500', 'IKEA BILLY Kitaplık', 0),
('prod-9', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500', 'Philips Hue Akıllı Ampul', 0),
('prod-10', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500', 'Sapiens Kitabı', 0);

-- Insert demo users (customers)
INSERT INTO users (id, email, name, role, is_active) VALUES
('user-1', '<EMAIL>', 'Ahmet Yılmaz', 'USER', true),
('user-2', '<EMAIL>', 'Ayşe Kaya', 'USER', true),
('user-3', '<EMAIL>', 'Mehmet Demir', 'USER', true),
('user-4', '<EMAIL>', 'Fatma Özkan', 'USER', true),
('user-5', '<EMAIL>', 'Ali Çelik', 'USER', true);

-- Insert demo profiles
INSERT INTO profiles (user_id, phone, date_of_birth, gender) VALUES
('user-1', '+90 ************', '1990-05-15', 'male'),
('user-2', '+90 ************', '1985-08-22', 'female'),
('user-3', '+90 ************', '1992-12-03', 'male'),
('user-4', '+90 ************', '1988-03-18', 'female'),
('user-5', '+90 ************', '1995-07-09', 'male');

-- Insert demo addresses
INSERT INTO addresses (user_id, title, first_name, last_name, phone, address, district, city, postal_code, is_default, is_active) VALUES
('user-1', 'Ev', 'Ahmet', 'Yılmaz', '+90 ************', 'Atatürk Mahallesi, Cumhuriyet Caddesi No: 123/5', 'Kadıköy', 'İstanbul', '34710', true, true),
('user-2', 'İş', 'Ayşe', 'Kaya', '+90 ************', 'Kızılay Mahallesi, Atatürk Bulvarı No: 456/12', 'Çankaya', 'Ankara', '06420', true, true),
('user-3', 'Ev', 'Mehmet', 'Demir', '+90 ************', 'Alsancak Mahallesi, Kordon Boyu No: 789/3', 'Konak', 'İzmir', '35220', true, true),
('user-4', 'Ev', 'Fatma', 'Özkan', '+90 ************', 'Lara Mahallesi, Atatürk Caddesi No: 321/8', 'Muratpaşa', 'Antalya', '07230', true, true),
('user-5', 'İş', 'Ali', 'Çelik', '+90 ************', 'Samsun Mahallesi, İnönü Bulvarı No: 654/15', 'İlkadım', 'Samsun', '55200', true, true);

-- Insert demo orders
INSERT INTO orders (id, user_id, order_number, status, payment_status, payment_method, total_amount, shipping_amount, tax_amount, notes) VALUES
('order-1', 'user-1', 'ORD-2024-001', 'DELIVERED', 'PAID', 'CREDIT_CARD', 55998.00, 0.00, 9999.64, 'Hızlı teslimat talep edildi'),
('order-2', 'user-2', 'ORD-2024-002', 'SHIPPED', 'PAID', 'CREDIT_CARD', 4298.00, 99.00, 769.64, null),
('order-3', 'user-3', 'ORD-2024-003', 'PROCESSING', 'PAID', 'BANK_TRANSFER', 43998.00, 0.00, 7879.64, 'Kurumsal fatura talep edildi'),
('order-4', 'user-4', 'ORD-2024-004', 'CONFIRMED', 'PAID', 'CREDIT_CARD', 1798.00, 29.00, 327.64, null),
('order-5', 'user-5', 'ORD-2024-005', 'PENDING', 'PENDING', 'CREDIT_CARD', 989.00, 29.00, 181.64, 'Ödeme beklemede');

-- Insert demo order items
INSERT INTO order_items (order_id, product_id, quantity, price, total_price) VALUES
('order-1', 'prod-1', 1, 54999.00, 54999.00),
('order-1', 'prod-9', 1, 899.00, 899.00),
('order-1', 'prod-10', 1, 89.00, 89.00),
('order-2', 'prod-5', 1, 4199.00, 4199.00),
('order-2', 'prod-10', 1, 89.00, 89.00),
('order-3', 'prod-3', 1, 42999.00, 42999.00),
('order-3', 'prod-9', 1, 899.00, 899.00),
('order-3', 'prod-10', 1, 89.00, 89.00),
('order-4', 'prod-6', 2, 899.00, 1798.00),
('order-5', 'prod-7', 1, 1299.00, 1299.00),
('order-5', 'prod-10', 1, 89.00, 89.00);

-- Update order timestamps for realistic data
UPDATE orders SET 
  created_at = NOW() - INTERVAL '5 days',
  updated_at = NOW() - INTERVAL '5 days'
WHERE id = 'order-1';

UPDATE orders SET 
  created_at = NOW() - INTERVAL '3 days',
  updated_at = NOW() - INTERVAL '2 days',
  shipped_at = NOW() - INTERVAL '1 day'
WHERE id = 'order-2';

UPDATE orders SET 
  created_at = NOW() - INTERVAL '2 days',
  updated_at = NOW() - INTERVAL '1 day'
WHERE id = 'order-3';

UPDATE orders SET 
  created_at = NOW() - INTERVAL '1 day',
  updated_at = NOW() - INTERVAL '1 day'
WHERE id = 'order-4';

UPDATE orders SET 
  created_at = NOW() - INTERVAL '6 hours',
  updated_at = NOW() - INTERVAL '6 hours'
WHERE id = 'order-5';

-- Insert order status history
INSERT INTO order_status_history (order_id, status, notes, changed_at) VALUES
('order-1', 'PENDING', 'Sipariş alındı', NOW() - INTERVAL '5 days'),
('order-1', 'CONFIRMED', 'Sipariş onaylandı', NOW() - INTERVAL '4 days'),
('order-1', 'PROCESSING', 'Sipariş hazırlanıyor', NOW() - INTERVAL '3 days'),
('order-1', 'SHIPPED', 'Sipariş kargoya verildi', NOW() - INTERVAL '2 days'),
('order-1', 'DELIVERED', 'Sipariş teslim edildi', NOW() - INTERVAL '1 day'),
('order-2', 'PENDING', 'Sipariş alındı', NOW() - INTERVAL '3 days'),
('order-2', 'CONFIRMED', 'Sipariş onaylandı', NOW() - INTERVAL '2 days'),
('order-2', 'SHIPPED', 'Sipariş kargoya verildi', NOW() - INTERVAL '1 day'),
('order-3', 'PENDING', 'Sipariş alındı', NOW() - INTERVAL '2 days'),
('order-3', 'CONFIRMED', 'Sipariş onaylandı', NOW() - INTERVAL '1 day'),
('order-3', 'PROCESSING', 'Sipariş hazırlanıyor', NOW() - INTERVAL '1 day'),
('order-4', 'PENDING', 'Sipariş alındı', NOW() - INTERVAL '1 day'),
('order-4', 'CONFIRMED', 'Sipariş onaylandı', NOW() - INTERVAL '1 day'),
('order-5', 'PENDING', 'Sipariş alındı', NOW() - INTERVAL '6 hours');

-- Note: To create an admin user, use the admin setup page at /admin/setup
-- with the setup key: demo-setup-key-2024
