import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET() {
  try {
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        isFeatured: true,
        status: 'ACTIVE',
      },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' }
        },
        reviews: {
          select: {
            rating: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 8,
    })
    
    // Transform products with average rating
    const transformedProducts = products.map(product => ({
      ...product,
      averageRating: product.reviews.length > 0 
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
        : 0,
      reviewCount: product.reviews.length,
      reviews: undefined, // Remove reviews from response
    }))
    
    return NextResponse.json({
      success: true,
      data: transformedProducts
    })
    
  } catch (error) {
    console.error('Error fetching featured products:', error)
    return NextResponse.json(
      { success: false, error: '<PERSON><PERSON> çıkan ürünler yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}
