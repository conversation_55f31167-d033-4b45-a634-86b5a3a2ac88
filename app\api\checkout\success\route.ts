import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'
import { retrieveCheckoutSession } from '@/lib/stripe/config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('session_id')
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID gereklidir' },
        { status: 400 }
      )
    }
    
    // Retrieve checkout session from Stripe
    const session = await retrieveCheckoutSession(sessionId)
    
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Ödeme oturumu bulunamadı' },
        { status: 404 }
      )
    }
    
    if (session.payment_status !== 'paid') {
      return NextResponse.json(
        { success: false, error: 'Ödeme tamamlanmamış' },
        { status: 400 }
      )
    }
    
    const supabase = createServerClient()
    
    // Get metadata
    const metadata = session.metadata
    if (!metadata?.user_id || !metadata?.address_id || !metadata?.items) {
      return NextResponse.json(
        { success: false, error: 'Eksik sipariş bilgileri' },
        { status: 400 }
      )
    }
    
    const userId = metadata.user_id
    const addressId = metadata.address_id
    const items = JSON.parse(metadata.items)
    const notes = metadata.notes || undefined
    
    // Check if order already exists
    const { data: existingOrder } = await supabase
      .from('orders')
      .select('id')
      .eq('payment_intent_id', session.payment_intent as string)
      .single()
    
    if (existingOrder) {
      return NextResponse.json({
        success: true,
        data: { orderId: existingOrder.id, alreadyExists: true }
      })
    }
    
    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`
    
    // Calculate totals from session
    const totalAmount = (session.amount_total || 0) / 100 // Convert from cents
    const shippingCost = session.shipping_cost?.amount_total ? (session.shipping_cost.amount_total / 100) : 0
    const taxAmount = (session.total_details?.amount_tax || 0) / 100
    
    // Create order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: userId,
        order_number: orderNumber,
        address_id: addressId,
        total_amount: totalAmount,
        shipping_cost: shippingCost,
        tax_amount: taxAmount,
        payment_method: 'stripe',
        payment_status: 'PAID',
        status: 'CONFIRMED',
        notes,
        payment_intent_id: session.payment_intent as string,
        stripe_session_id: sessionId,
      })
      .select('id')
      .single()
    
    if (orderError) {
      console.error('Order creation error:', orderError)
      return NextResponse.json(
        { success: false, error: 'Sipariş oluşturulurken hata oluştu' },
        { status: 500 }
      )
    }
    
    // Create order items
    const orderItems = items.map((item: any) => ({
      order_id: order.id,
      product_id: item.productId,
      quantity: item.quantity,
      price: item.price,
      total: item.quantity * item.price,
    }))
    
    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems)
    
    if (itemsError) {
      console.error('Order items creation error:', itemsError)
      // Don't fail the request, order is already created
    }
    
    // Update product stock
    for (const item of items) {
      const { error: stockError } = await supabase.rpc('decrease_product_stock', {
        product_id: item.productId,
        quantity: item.quantity
      })
      
      if (stockError) {
        console.error('Stock update error:', stockError)
        // Don't fail the request, continue with other items
      }
    }
    
    // Clear user's cart
    const { error: cartError } = await supabase
      .from('cart_items')
      .delete()
      .eq('user_id', userId)
    
    if (cartError) {
      console.error('Cart clear error:', cartError)
      // Don't fail the request
    }
    
    // TODO: Send order confirmation email
    
    return NextResponse.json({
      success: true,
      data: { 
        orderId: order.id,
        orderNumber,
        totalAmount,
        paymentStatus: 'paid'
      }
    })
    
  } catch (error) {
    console.error('Checkout success error:', error)
    return NextResponse.json(
      { success: false, error: 'Sipariş işlenirken hata oluştu' },
      { status: 500 }
    )
  }
}
