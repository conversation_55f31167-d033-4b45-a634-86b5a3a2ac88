'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ProductCard } from '@/components/products/product-card'
import { 
  Heart, 
  ArrowLeft,
  ShoppingBag
} from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/lib/store/auth-store'
import { useRouter } from 'next/navigation'
import { Product } from '@/types'

export default function FavoritesPage() {
  const { user, isAuthenticated } = useAuthStore()
  const router = useRouter()
  const [favorites, setFavorites] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/profile/favorites')
      return
    }
    
    // Mock data for demonstration
    const mockFavorites: Product[] = []
    
    setFavorites(mockFavorites)
    setIsLoading(false)
  }, [isAuthenticated, router])

  if (!isAuthenticated) {
    return null
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/profile">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Favorilerim</h1>
            <p className="text-gray-600">Beğendiğiniz ürünler</p>
          </div>
        </div>

        {favorites.length > 0 ? (
          <>
            {/* Stats */}
            <div className="mb-6">
              <p className="text-sm text-gray-600">
                Toplam {favorites.length} ürün favorilerinizde
              </p>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {favorites.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </>
        ) : (
          /* Empty State */
          <Card className="text-center py-16">
            <CardContent>
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <Heart className="h-8 w-8 text-gray-400" />
                </div>
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                Henüz favori ürününüz yok
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Beğendiğiniz ürünleri favorilere ekleyerek daha sonra kolayca bulabilirsiniz.
              </p>
              <Button asChild>
                <Link href="/shop">
                  <ShoppingBag className="h-4 w-4 mr-2" />
                  Alışverişe Başla
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
