'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, EyeOff, Shield, User, Mail, Lock, Key } from 'lucide-react'
import toast from 'react-hot-toast'

const setupSchema = z.object({
  name: z.string().min(2, 'Ad en az 2 karakter olmalıdır'),
  email: z.string().email('Geçerli bir e-posta adresi girin'),
  password: z.string().min(6, 'Şifre en az 6 karakter olmalıdır'),
  confirmPassword: z.string(),
  setupKey: z.string().min(1, '<PERSON><PERSON><PERSON> anahtarı gereklidir'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Şifreler eşleşmiyor",
  path: ["confirmPassword"],
})

type SetupForm = z.infer<typeof setupSchema>

export default function AdminSetupPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SetupForm>({
    resolver: zodResolver(setupSchema),
  })
  
  const onSubmit = async (data: SetupForm) => {
    setIsCreating(true)
    
    try {
      const response = await fetch('/api/admin/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Admin kullanıcısı başarıyla oluşturuldu!')
        router.push('/auth/login?message=Admin kullanıcısı oluşturuldu. Giriş yapabilirsiniz.')
      } else {
        toast.error(result.error || 'Admin kullanıcısı oluşturulurken hata oluştu')
      }
    } catch (error) {
      console.error('Setup error:', error)
      toast.error('Admin kullanıcısı oluşturulurken hata oluştu')
    } finally {
      setIsCreating(false)
    }
  }
  
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary rounded-full flex items-center justify-center mb-4">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <CardTitle className="text-2xl">Admin Kurulumu</CardTitle>
            <CardDescription>
              İlk admin kullanıcısını oluşturun
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Alert className="mb-6">
              <Key className="h-4 w-4" />
              <AlertDescription>
                Bu sayfa sadece ilk admin kullanıcısını oluşturmak için kullanılır. 
                Kurulum anahtarına ihtiyacınız vardır.
              </AlertDescription>
            </Alert>
            
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Ad Soyad</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="name"
                    type="text"
                    placeholder="Admin Adı"
                    className="pl-10"
                    {...register('name')}
                  />
                </div>
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>
              
              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email">E-posta</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-10"
                    {...register('email')}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>
              
              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password">Şifre</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="••••••••"
                    className="pl-10 pr-10"
                    {...register('password')}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>
              
              {/* Confirm Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Şifre Tekrar</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="••••••••"
                    className="pl-10 pr-10"
                    {...register('confirmPassword')}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
                )}
              </div>
              
              {/* Setup Key */}
              <div className="space-y-2">
                <Label htmlFor="setupKey">Kurulum Anahtarı</Label>
                <div className="relative">
                  <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="setupKey"
                    type="password"
                    placeholder="Kurulum anahtarını girin"
                    className="pl-10"
                    {...register('setupKey')}
                  />
                </div>
                {errors.setupKey && (
                  <p className="text-sm text-red-600">{errors.setupKey.message}</p>
                )}
              </div>
              
              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full"
                disabled={isCreating}
              >
                {isCreating ? 'Admin oluşturuluyor...' : 'Admin Oluştur'}
              </Button>
            </form>
            
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Zaten admin hesabınız var mı?{' '}
                <a
                  href="/auth/login"
                  className="text-primary hover:underline font-medium"
                >
                  Giriş Yap
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
        
        {/* Demo Info */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <h3 className="font-medium text-blue-900 mb-2">Demo Kurulum</h3>
            <p className="text-sm text-blue-700 mb-2">
              Test için aşağıdaki bilgileri kullanabilirsiniz:
            </p>
            <div className="text-sm text-blue-600 space-y-1">
              <p><strong>Kurulum Anahtarı:</strong> demo-setup-key-2024</p>
              <p><strong>Önerilen E-posta:</strong> <EMAIL></p>
              <p><strong>Önerilen Şifre:</strong> admin123</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
