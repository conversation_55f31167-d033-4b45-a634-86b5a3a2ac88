// Admin User Creation Script
// Run this with: node scripts/create-admin.js

// Use built-in fetch for Node.js 18+
const fetch = globalThis.fetch || require('node-fetch');

const ADMIN_SETUP_URL = 'http://localhost:3000/api/admin/setup';

const adminData = {
  name: 'Admin User',
  email: '<EMAIL>',
  password: 'admin123',
  confirmPassword: 'admin123',
  setupKey: 'demo-setup-key-2024'
};

async function createAdminUser() {
  try {
    console.log('🔧 Admin kullanıcısı oluşturuluyor...');
    
    const response = await fetch(ADMIN_SETUP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(adminData),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Ad<PERSON> kullanıcısı başarıyla oluşturuldu!');
      console.log('📧 E-posta:', adminData.email);
      console.log('🔑 Şifre:', adminData.password);
      console.log('🌐 Admin Panel:', 'http://localhost:3000/admin');
    } else {
      console.error('❌ Hata:', result.error);
    }
  } catch (error) {
    console.error('❌ Bağlantı hatası:', error.message);
    console.log('💡 Sunucunun çalıştığından emin olun: npm run dev');
  }
}

// Check if admin already exists
async function checkAdminExists() {
  try {
    const response = await fetch(ADMIN_SETUP_URL);
    const result = await response.json();
    
    if (result.success && !result.data.setupRequired) {
      console.log('ℹ️  Admin kullanıcısı zaten mevcut');
      console.log('🌐 Admin Panel:', 'http://localhost:3000/admin');
      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Admin durumu kontrol edilemedi:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 E-Marketing Admin Setup');
  console.log('==========================');
  
  const adminExists = await checkAdminExists();
  
  if (!adminExists) {
    await createAdminUser();
  }
  
  console.log('\n📋 Demo Giriş Bilgileri:');
  console.log('=======================');
  console.log('Admin Panel: http://localhost:3000/admin');
  console.log('E-posta: <EMAIL>');
  console.log('Şifre: admin123');
  console.log('\nDemo Müşteri Hesapları:');
  console.log('- <EMAIL>');
  console.log('- <EMAIL>');
  console.log('- <EMAIL>');
  console.log('(Şifre: demo123 - Supabase Auth\'da manuel oluşturulmalı)');
}

if (require.main === module) {
  main();
}

module.exports = { createAdminUser, checkAdminExists };
