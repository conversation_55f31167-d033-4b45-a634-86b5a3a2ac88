generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String?
  phone     String?
  avatar    String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  profile   Profile?
  addresses Address[]
  orders    Order[]
  cartItems CartItem[]
  favorites Favorite[]
  reviews   Review[]

  @@map("users")
}

model Profile {
  id          String    @id @default(uuid())
  userId      String    @unique
  firstName   String?
  lastName    String?
  dateOfBirth DateTime?
  gender      Gender?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model Address {
  id          String      @id @default(uuid())
  userId      String
  title       String
  firstName   String
  lastName    String
  phone       String
  address     String
  district    String
  city        String
  postalCode  String
  isDefault   Boolean     @default(false)
  addressType AddressType @default(HOME)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  @@map("addresses")
}

model Category {
  id          String    @id @default(uuid())
  name        String
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

model Product {
  id          String        @id @default(uuid())
  name        String
  slug        String        @unique
  description String?
  price       Decimal       @db.Decimal(10, 2)
  comparePrice Decimal?     @db.Decimal(10, 2)
  sku         String?       @unique
  stock       Int           @default(0)
  isActive    Boolean       @default(true)
  isFeatured  Boolean       @default(false)
  weight      Decimal?      @db.Decimal(8, 2)
  dimensions  String?
  status      ProductStatus @default(DRAFT)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  categoryId    String
  category      Category       @relation(fields: [categoryId], references: [id])
  images        ProductImage[]
  cartItems     CartItem[]
  orderItems    OrderItem[]
  favorites     Favorite[]
  reviews       Review[]
  productTags   ProductTag[]

  @@map("products")
}

model ProductImage {
  id        String   @id @default(uuid())
  productId String
  url       String
  alt       String?
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model Tag {
  id          String       @id @default(uuid())
  name        String       @unique
  slug        String       @unique
  createdAt   DateTime     @default(now())
  productTags ProductTag[]

  @@map("tags")
}

model ProductTag {
  productId String
  tagId     String

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([productId, tagId])
  @@map("product_tags")
}

model CartItem {
  id        String   @id @default(uuid())
  userId    String
  productId String
  quantity  Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

model Order {
  id            String      @id @default(uuid())
  userId        String
  orderNumber   String      @unique
  status        OrderStatus @default(PENDING)
  totalAmount   Decimal     @db.Decimal(10, 2)
  shippingCost  Decimal     @default(0) @db.Decimal(10, 2)
  taxAmount     Decimal     @default(0) @db.Decimal(10, 2)
  discountAmount Decimal    @default(0) @db.Decimal(10, 2)
  paymentMethod String?
  paymentStatus PaymentStatus @default(PENDING)
  notes         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id])
  addressId    String
  address      Address      @relation(fields: [addressId], references: [id])
  orderItems   OrderItem[]
  shippingInfo ShippingInfo?

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(uuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model ShippingInfo {
  id            String         @id @default(uuid())
  orderId       String         @unique
  carrier       String
  trackingNumber String?
  status        ShippingStatus @default(PREPARING)
  shippedAt     DateTime?
  deliveredAt   DateTime?
  estimatedDelivery DateTime?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("shipping_info")
}

model Favorite {
  id        String   @id @default(uuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("favorites")
}

model Review {
  id        String   @id @default(uuid())
  userId    String
  productId String
  rating    Int      @db.SmallInt
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

// Enums
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum AddressType {
  HOME
  WORK
  OTHER
}

enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum ShippingStatus {
  PREPARING
  SHIPPED
  IN_TRANSIT
  OUT_FOR_DELIVERY
  DELIVERED
  RETURNED
}
