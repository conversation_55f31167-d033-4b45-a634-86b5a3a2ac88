export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string | null
          phone: string | null
          avatar: string | null
          role: 'USER' | 'ADMIN' | 'SUPER_ADMIN'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          phone?: string | null
          avatar?: string | null
          role?: 'USER' | 'ADMIN' | 'SUPER_ADMIN'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          phone?: string | null
          avatar?: string | null
          role?: 'USER' | 'ADMIN' | 'SUPER_ADMIN'
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          user_id: string
          first_name: string | null
          last_name: string | null
          date_of_birth: string | null
          gender: 'MALE' | 'FEMALE' | 'OTHER' | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          first_name?: string | null
          last_name?: string | null
          date_of_birth?: string | null
          gender?: 'MALE' | 'FEMALE' | 'OTHER' | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          first_name?: string | null
          last_name?: string | null
          date_of_birth?: string | null
          gender?: 'MALE' | 'FEMALE' | 'OTHER' | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          image: string | null
          parent_id: string | null
          is_active: boolean
          sort_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          image?: string | null
          parent_id?: string | null
          is_active?: boolean
          sort_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          image?: string | null
          parent_id?: string | null
          is_active?: boolean
          sort_order?: number
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          price: number
          compare_price: number | null
          sku: string | null
          stock: number
          is_active: boolean
          is_featured: boolean
          weight: number | null
          dimensions: string | null
          status: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
          category_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          price: number
          compare_price?: number | null
          sku?: string | null
          stock?: number
          is_active?: boolean
          is_featured?: boolean
          weight?: number | null
          dimensions?: string | null
          status?: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
          category_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          price?: number
          compare_price?: number | null
          sku?: string | null
          stock?: number
          is_active?: boolean
          is_featured?: boolean
          weight?: number | null
          dimensions?: string | null
          status?: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
          category_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          user_id: string
          order_number: string
          status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
          total_amount: number
          shipping_cost: number
          tax_amount: number
          discount_amount: number
          payment_method: string | null
          payment_status: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'
          notes: string | null
          address_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          order_number: string
          status?: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
          total_amount: number
          shipping_cost?: number
          tax_amount?: number
          discount_amount?: number
          payment_method?: string | null
          payment_status?: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'
          notes?: string | null
          address_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          order_number?: string
          status?: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
          total_amount?: number
          shipping_cost?: number
          tax_amount?: number
          discount_amount?: number
          payment_method?: string | null
          payment_status?: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'
          notes?: string | null
          address_id?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
