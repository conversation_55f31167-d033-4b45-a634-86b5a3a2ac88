import Stripe from 'stripe'

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables')
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
  typescript: true,
})

export const STRIPE_CONFIG = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  currency: 'try',
  paymentMethods: ['card'],
}

// Stripe webhook events we handle
export const STRIPE_WEBHOOK_EVENTS = {
  PAYMENT_INTENT_SUCCEEDED: 'payment_intent.succeeded',
  PAYMENT_INTENT_PAYMENT_FAILED: 'payment_intent.payment_failed',
  CHECKOUT_SESSION_COMPLETED: 'checkout.session.completed',
  INVOICE_PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',
  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',
} as const

// Helper function to format amount for Stripe (convert to cents)
export function formatAmountForStripe(amount: number, currency: string = 'try'): number {
  const numberFormat = new Intl.NumberFormat(['tr-TR'], {
    style: 'currency',
    currency: currency.toUpperCase(),
    currencyDisplay: 'symbol',
  })
  
  const parts = numberFormat.formatToParts(amount)
  let zeroDecimalCurrency = false
  
  // Check if currency has zero decimal places
  const zeroDecimalCurrencies = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF']
  if (zeroDecimalCurrencies.includes(currency.toUpperCase())) {
    zeroDecimalCurrency = true
  }
  
  return zeroDecimalCurrency ? Math.round(amount) : Math.round(amount * 100)
}

// Helper function to format amount from Stripe (convert from cents)
export function formatAmountFromStripe(amount: number, currency: string = 'try'): number {
  const zeroDecimalCurrencies = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF']
  const zeroDecimalCurrency = zeroDecimalCurrencies.includes(currency.toUpperCase())
  
  return zeroDecimalCurrency ? amount : amount / 100
}

// Create payment intent
export async function createPaymentIntent(
  amount: number,
  currency: string = 'try',
  metadata?: Record<string, string>
): Promise<Stripe.PaymentIntent> {
  return await stripe.paymentIntents.create({
    amount: formatAmountForStripe(amount, currency),
    currency: currency.toLowerCase(),
    automatic_payment_methods: {
      enabled: true,
    },
    metadata,
  })
}

// Create checkout session
export async function createCheckoutSession(
  lineItems: Stripe.Checkout.SessionCreateParams.LineItem[],
  metadata?: Record<string, string>,
  successUrl?: string,
  cancelUrl?: string
): Promise<Stripe.Checkout.Session> {
  return await stripe.checkout.sessions.create({
    mode: 'payment',
    payment_method_types: ['card'],
    line_items: lineItems,
    success_url: successUrl || `${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: cancelUrl || `${process.env.NEXT_PUBLIC_APP_URL}/checkout/cancel`,
    metadata,
    shipping_address_collection: {
      allowed_countries: ['TR'],
    },
    billing_address_collection: 'required',
    phone_number_collection: {
      enabled: true,
    },
  })
}

// Retrieve checkout session
export async function retrieveCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session> {
  return await stripe.checkout.sessions.retrieve(sessionId, {
    expand: ['line_items', 'payment_intent'],
  })
}

// Create customer
export async function createStripeCustomer(
  email: string,
  name?: string,
  phone?: string,
  metadata?: Record<string, string>
): Promise<Stripe.Customer> {
  return await stripe.customers.create({
    email,
    name,
    phone,
    metadata,
  })
}

// Update customer
export async function updateStripeCustomer(
  customerId: string,
  data: Stripe.CustomerUpdateParams
): Promise<Stripe.Customer> {
  return await stripe.customers.update(customerId, data)
}

// Create refund
export async function createRefund(
  paymentIntentId: string,
  amount?: number,
  reason?: Stripe.RefundCreateParams.Reason
): Promise<Stripe.Refund> {
  const refundData: Stripe.RefundCreateParams = {
    payment_intent: paymentIntentId,
  }
  
  if (amount) {
    refundData.amount = formatAmountForStripe(amount)
  }
  
  if (reason) {
    refundData.reason = reason
  }
  
  return await stripe.refunds.create(refundData)
}
