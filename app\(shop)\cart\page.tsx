'use client'

import { useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Plus, Minus, Trash2, ShoppingCart, ArrowLeft } from 'lucide-react'
import { useCartStore } from '@/lib/store/cart-store'
import { formatPrice } from '@/lib/utils'
import toast from 'react-hot-toast'

export default function CartPage() {
  const {
    items,
    totalItems,
    totalPrice,
    isLoading,
    isUpdating,
    updateQuantity,
    removeItem,
    clearCart,
    fetchCart,
  } = useCartStore()
  
  useEffect(() => {
    fetchCart()
  }, [fetchCart])
  
  const handleUpdateQuantity = async (itemId: string, quantity: number) => {
    const result = await updateQuantity(itemId, quantity)
    if (!result.success) {
      toast.error(result.error || '<PERSON><PERSON><PERSON> g<PERSON> hata oluştu')
    }
  }
  
  const handleRemoveItem = async (itemId: string) => {
    const result = await removeItem(itemId)
    if (result.success) {
      toast.success('Ürün sepetten çıkarıldı')
    } else {
      toast.error(result.error || 'Ürün çıkarılırken hata oluştu')
    }
  }
  
  const handleClearCart = async () => {
    if (window.confirm('Sepeti temizlemek istediğinizden emin misiniz?')) {
      const result = await clearCart()
      if (result.success) {
        toast.success('Sepet temizlendi')
      } else {
        toast.error(result.error || 'Sepet temizlenirken hata oluştu')
      }
    }
  }
  
  const shippingCost = totalPrice >= 150 ? 0 : 29.99
  const finalTotal = totalPrice + shippingCost
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Sepetim ({totalItems} ürün)
              </h1>
              <p className="text-gray-600">
                Alışverişinizi tamamlamak için ürünlerinizi kontrol edin
              </p>
            </div>
            <Button variant="outline" asChild>
              <Link href="/shop">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Alışverişe Devam Et
              </Link>
            </Button>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        {items.length === 0 ? (
          /* Empty Cart */
          <div className="text-center py-16">
            <ShoppingCart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Sepetiniz boş
            </h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Henüz sepetinizde ürün bulunmuyor. Alışverişe başlamak için ürünlerimizi keşfedin.
            </p>
            <Button size="lg" asChild>
              <Link href="/shop">Alışverişe Başla</Link>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Ürünler</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearCart}
                  disabled={isUpdating}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Sepeti Temizle
                </Button>
              </div>
              
              <div className="space-y-4">
                {items.map((item) => (
                  <Card key={item.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        {/* Product Image */}
                        <div className="relative w-24 h-24 flex-shrink-0">
                          <Image
                            src={item.product.images?.[0]?.url || '/placeholder-product.jpg'}
                            alt={item.product.name}
                            fill
                            className="object-cover rounded-md"
                          />
                        </div>
                        
                        {/* Product Info */}
                        <div className="flex-1 min-w-0">
                          <Link
                            href={`/products/${item.product.slug}`}
                            className="text-lg font-medium text-gray-900 hover:text-primary transition-colors"
                          >
                            {item.product.name}
                          </Link>
                          <p className="text-sm text-gray-500 mt-1">
                            {item.product.category?.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            SKU: {item.product.sku || 'N/A'}
                          </p>
                          
                          {/* Stock Status */}
                          <div className="flex items-center mt-2">
                            <div className={`w-2 h-2 rounded-full mr-2 ${
                              item.product.stock > 0 ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                            <span className={`text-sm ${
                              item.product.stock > 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {item.product.stock > 0 
                                ? `Stokta (${item.product.stock} adet)` 
                                : 'Stokta Yok'
                              }
                            </span>
                          </div>
                        </div>
                        
                        {/* Price and Controls */}
                        <div className="text-right space-y-4">
                          <div>
                            <p className="text-lg font-bold text-gray-900">
                              {formatPrice(item.product.price)}
                            </p>
                            {item.product.comparePrice && item.product.comparePrice > item.product.price && (
                              <p className="text-sm text-gray-500 line-through">
                                {formatPrice(item.product.comparePrice)}
                              </p>
                            )}
                          </div>
                          
                          {/* Quantity Controls */}
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                              disabled={isUpdating || item.quantity <= 1}
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            
                            <span className="text-lg font-medium min-w-[3rem] text-center">
                              {item.quantity}
                            </span>
                            
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                              disabled={isUpdating || item.quantity >= item.product.stock}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          
                          {/* Remove Button */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(item.id)}
                            disabled={isUpdating}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Kaldır
                          </Button>
                        </div>
                      </div>
                      
                      {/* Item Total */}
                      <div className="flex justify-end mt-4 pt-4 border-t">
                        <div className="text-right">
                          <p className="text-sm text-gray-600">Ara Toplam</p>
                          <p className="text-xl font-bold text-gray-900">
                            {formatPrice(item.quantity * item.product.price)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle>Sipariş Özeti</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Ara Toplam ({totalItems} ürün)</span>
                    <span className="font-medium">{formatPrice(totalPrice)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span>Kargo</span>
                    <span className={`font-medium ${shippingCost === 0 ? 'text-green-600' : ''}`}>
                      {shippingCost === 0 ? 'Ücretsiz' : formatPrice(shippingCost)}
                    </span>
                  </div>
                  
                  {totalPrice < 150 && (
                    <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
                      <p className="font-medium text-blue-800 mb-1">
                        Ücretsiz kargo için {formatPrice(150 - totalPrice)} daha ekleyin!
                      </p>
                      <div className="w-full bg-blue-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(totalPrice / 150) * 100}%` }}
                        />
                      </div>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div className="flex justify-between text-lg font-bold">
                    <span>Toplam</span>
                    <span>{formatPrice(finalTotal)}</span>
                  </div>
                  
                  <Button className="w-full" size="lg" asChild>
                    <Link href="/checkout">Satın Al</Link>
                  </Button>
                  
                  <div className="text-center">
                    <p className="text-xs text-gray-500">
                      Güvenli ödeme ile korumalı alışveriş
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
