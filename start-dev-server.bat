@echo off
echo ========================================
echo    E-Marketing Development Server
echo ========================================
echo.

echo Proje klasorune gidiliyor...
cd /d "C:\Users\<USER>\Desktop\e-marketing"

echo.
echo Node.js versiyonu kontrol ediliyor...
node --version
npm --version

echo.
echo Dependencies kontrol ediliyor...
if not exist "node_modules" (
    echo Dependencies bulunamadi, yukleniyor...
    npm install
) else (
    echo Dependencies mevcut.
)

echo.
echo Development server baslatiliyor...
echo.
echo Ana site: http://localhost:3000
echo Preview: file:///C:/Users/<USER>/Desktop/e-marketing/preview.html
echo Admin: file:///C:/Users/<USER>/Desktop/e-marketing/admin-panel.html
echo.
echo Server'i durdurmak icin Ctrl+C basin
echo.

npm run dev

pause
