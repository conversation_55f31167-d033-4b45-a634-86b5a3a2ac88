/**
 * Data Bridge - Admin Panel ve Ana Site Arasında Veri Entegrasyonu
 * Bu dosya admin panel ile ana site arasında veri senkronizasyonu sağlar
 */

class DataBridge {
  constructor() {
    this.storageKey = 'e-marketing-data';
    this.apiBaseUrl = '/api';
    this.isStandalone = window.location.pathname.includes('admin-panel.html');
    this.init();
  }

  init() {
    // Storage event listener for real-time sync
    window.addEventListener('storage', (e) => {
      if (e.key === this.storageKey) {
        this.handleDataChange(e.newValue);
      }
    });

    // Initialize data if not exists
    if (!this.getData()) {
      this.initializeData();
    }
  }

  // Get all data from storage
  getData() {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting data:', error);
      return null;
    }
  }

  // Set data to storage
  setData(data) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      this.notifyDataChange(data);

      // Sync with API if not in standalone mode
      if (!this.isStandalone) {
        this.syncWithAPI(data);
      }

      return true;
    } catch (error) {
      console.error('Error setting data:', error);
      return false;
    }
  }

  // Sync data with API
  async syncWithAPI(data) {
    try {
      // Only sync if we're in the main application (not admin-panel.html)
      if (this.isStandalone) return;

      // Sync products with API
      if (data.products) {
        for (const product of data.products) {
          if (product._needsSync) {
            await this.syncProductWithAPI(product);
            delete product._needsSync;
          }
        }
      }

      // Update localStorage after sync
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Error syncing with API:', error);
    }
  }

  // Sync individual product with API
  async syncProductWithAPI(product) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product),
      });

      if (!response.ok) {
        console.error('Failed to sync product with API:', response.statusText);
      }
    } catch (error) {
      console.error('Error syncing product with API:', error);
    }
  }

  // Initialize default data structure
  initializeData() {
    const defaultData = {
      products: [
        {
          id: '1',
          name: 'iPhone 15 Pro',
          slug: 'iphone-15-pro',
          description: 'Apple iPhone 15 Pro 128GB - Titanium Blue',
          price: 45999,
          comparePrice: 49999,
          sku: 'IPH15PRO001',
          stock: 15,
          isActive: true,
          isFeatured: true,
          categoryId: 'electronics',
          category: { id: 'electronics', name: 'Elektronik', slug: 'elektronik' },
          images: [
            {
              id: '1',
              url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
              alt: 'iPhone 15 Pro',
              sortOrder: 0
            }
          ],
          status: 'ACTIVE',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'MacBook Air M2',
          slug: 'macbook-air-m2',
          description: 'Apple MacBook Air M2 256GB - Space Gray',
          price: 32999,
          comparePrice: 35999,
          sku: 'MBA2024001',
          stock: 8,
          isActive: true,
          isFeatured: true,
          categoryId: 'electronics',
          category: { id: 'electronics', name: 'Elektronik', slug: 'elektronik' },
          images: [
            {
              id: '2',
              url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500',
              alt: 'MacBook Air M2',
              sortOrder: 0
            }
          ],
          status: 'ACTIVE',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Samsung Galaxy S24',
          slug: 'samsung-galaxy-s24',
          description: 'Samsung Galaxy S24 128GB - Phantom Black',
          price: 28999,
          comparePrice: 31999,
          sku: 'SGS24001',
          stock: 3,
          isActive: false,
          isFeatured: false,
          categoryId: 'electronics',
          category: { id: 'electronics', name: 'Elektronik', slug: 'elektronik' },
          images: [
            {
              id: '3',
              url: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',
              alt: 'Samsung Galaxy S24',
              sortOrder: 0
            }
          ],
          status: 'INACTIVE',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '4',
          name: 'Nike Air Max 270',
          slug: 'nike-air-max-270',
          description: 'Nike Air Max 270 Spor Ayakkabı - Siyah/Beyaz',
          price: 1299,
          comparePrice: 1499,
          sku: 'NAM270001',
          stock: 25,
          isActive: true,
          isFeatured: false,
          categoryId: 'sports',
          category: { id: 'sports', name: 'Spor', slug: 'spor' },
          images: [
            {
              id: '4',
              url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500',
              alt: 'Nike Air Max 270',
              sortOrder: 0
            }
          ],
          status: 'ACTIVE',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      categories: [
        {
          id: 'electronics',
          name: 'Elektronik',
          slug: 'elektronik',
          description: 'Telefon, bilgisayar ve elektronik ürünler',
          isActive: true,
          sortOrder: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'sports',
          name: 'Spor',
          slug: 'spor',
          description: 'Spor ayakkabı ve ekipmanları',
          isActive: true,
          sortOrder: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'clothing',
          name: 'Giyim',
          slug: 'giyim',
          description: 'Erkek ve kadın giyim ürünleri',
          isActive: true,
          sortOrder: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      users: [
        {
          id: '1',
          name: 'Ahmet Yılmaz',
          email: '<EMAIL>',
          role: 'USER',
          isActive: true,
          orderCount: 5,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
        },
        {
          id: '2',
          name: 'Fatma Demir',
          email: '<EMAIL>',
          role: 'ADMIN',
          isActive: true,
          orderCount: 12,
          createdAt: '2024-01-10T10:00:00Z',
          updatedAt: '2024-01-10T10:00:00Z',
          lastLoginAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
        },
        {
          id: '3',
          name: 'Mehmet Kaya',
          email: '<EMAIL>',
          role: 'USER',
          isActive: true,
          orderCount: 3,
          createdAt: '2024-01-20T10:00:00Z',
          updatedAt: '2024-01-20T10:00:00Z',
          lastLoginAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
        }
      ],
      orders: [
        {
          id: '1',
          orderNumber: 'ORD-001',
          userId: '1',
          user: { id: '1', name: 'Ahmet Yılmaz', email: '<EMAIL>' },
          status: 'DELIVERED',
          paymentStatus: 'PAID',
          totalAmount: 45999,
          items: [
            { id: '1', productId: '1', name: 'iPhone 15 Pro', quantity: 1, price: 45999 }
          ],
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          orderNumber: 'ORD-002',
          userId: '2',
          user: { id: '2', name: 'Fatma Demir', email: '<EMAIL>' },
          status: 'PROCESSING',
          paymentStatus: 'PAID',
          totalAmount: 1299,
          items: [
            { id: '2', productId: '4', name: 'Nike Air Max 270', quantity: 1, price: 1299 }
          ],
          createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '3',
          orderNumber: 'ORD-003',
          userId: '3',
          user: { id: '3', name: 'Mehmet Kaya', email: '<EMAIL>' },
          status: 'SHIPPED',
          paymentStatus: 'PAID',
          totalAmount: 32999,
          items: [
            { id: '3', productId: '2', name: 'MacBook Air M2', quantity: 1, price: 32999 }
          ],
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      siteSettings: {
        siteName: 'E-Marketing',
        siteDescription: 'Modern e-ticaret platformu',
        logo: '/logo.png',
        favicon: '/favicon.ico',
        primaryColor: '#3b82f6',
        secondaryColor: '#8b5cf6',
        contactEmail: '<EMAIL>',
        contactPhone: '(0212) 123 45 67',
        address: 'İstanbul, Türkiye',
        socialMedia: {
          facebook: 'https://facebook.com/e-marketing',
          twitter: 'https://twitter.com/e-marketing',
          instagram: 'https://instagram.com/e-marketing'
        },
        updatedAt: new Date().toISOString()
      },
      pageContents: {
        about: {
          title: 'Hakkımızda',
          subtitle: '2024 yılında kurulan E-Marketing, modern e-ticaret deneyimi sunan yenilikçi bir platformdur.',
          mission: 'Müşterilerimize en kaliteli ürünleri, en uygun fiyatlarla sunarak online alışveriş deneyimini mükemmelleştirmek.',
          vision: 'Türkiye\'nin en güvenilir ve tercih edilen e-ticaret platformu olmak.',
          updatedAt: new Date().toISOString()
        },
        home: {
          heroTitle: 'En İyi Ürünler, En Uygun Fiyatlar',
          heroSubtitle: 'Kaliteli ürünleri keşfedin ve güvenli alışveriş yapın',
          featuredTitle: 'Öne Çıkan Ürünler',
          updatedAt: new Date().toISOString()
        }
      }
    };

    this.setData(defaultData);
    return defaultData;
  }

  // Notify other windows/tabs about data changes
  notifyDataChange(data) {
    // Dispatch custom event for same-window components
    window.dispatchEvent(new CustomEvent('databridge:change', { detail: data }));
  }

  // Handle data changes from other windows/tabs
  handleDataChange(newDataString) {
    try {
      const newData = JSON.parse(newDataString);
      window.dispatchEvent(new CustomEvent('databridge:change', { detail: newData }));
    } catch (error) {
      console.error('Error handling data change:', error);
    }
  }

  // Product methods
  getProducts() {
    const data = this.getData();
    return data ? data.products : [];
  }

  getProduct(id) {
    const products = this.getProducts();
    return products.find(p => p.id === id);
  }

  addProduct(product) {
    const data = this.getData();
    if (!data) return false;

    const newProduct = {
      ...product,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      _needsSync: true
    };

    data.products.push(newProduct);
    return this.setData(data);
  }

  updateProduct(id, updates) {
    const data = this.getData();
    if (!data) return false;

    const index = data.products.findIndex(p => p.id === id);
    if (index === -1) return false;

    data.products[index] = {
      ...data.products[index],
      ...updates,
      updatedAt: new Date().toISOString(),
      _needsSync: true
    };

    return this.setData(data);
  }

  deleteProduct(id) {
    const data = this.getData();
    if (!data) return false;

    data.products = data.products.filter(p => p.id !== id);
    return this.setData(data);
  }

  // User methods
  getUsers() {
    const data = this.getData();
    return data ? data.users : [];
  }

  addUser(user) {
    const data = this.getData();
    if (!data) return false;

    const newUser = {
      ...user,
      id: this.generateId(),
      orderCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastLoginAt: null
    };

    data.users.push(newUser);
    return this.setData(data);
  }

  // Order methods
  getOrders() {
    const data = this.getData();
    return data ? data.orders : [];
  }

  updateOrderStatus(id, status) {
    const data = this.getData();
    if (!data) return false;

    const index = data.orders.findIndex(o => o.id === id);
    if (index === -1) return false;

    data.orders[index].status = status;
    data.orders[index].updatedAt = new Date().toISOString();

    return this.setData(data);
  }

  // Site settings methods
  getSiteSettings() {
    const data = this.getData();
    return data ? data.siteSettings : {};
  }

  updateSiteSettings(settings) {
    const data = this.getData();
    if (!data) return false;

    data.siteSettings = {
      ...data.siteSettings,
      ...settings,
      updatedAt: new Date().toISOString()
    };

    return this.setData(data);
  }

  // Page content methods
  getPageContent(page) {
    const data = this.getData();
    return data && data.pageContents ? data.pageContents[page] : {};
  }

  updatePageContent(page, content) {
    const data = this.getData();
    if (!data) return false;

    if (!data.pageContents) {
      data.pageContents = {};
    }

    data.pageContents[page] = {
      ...data.pageContents[page],
      ...content,
      updatedAt: new Date().toISOString()
    };

    return this.setData(data);
  }

  // Utility methods
  generateId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  formatPrice(price) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(price);
  }

  formatDate(date) {
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  }

  getRelativeTime(date) {
    const now = new Date();
    const past = new Date(date);
    const diffMs = now - past;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Az önce';
    } else if (diffHours < 24) {
      return `${diffHours} saat önce`;
    } else if (diffDays < 7) {
      return `${diffDays} gün önce`;
    } else {
      return this.formatDate(date);
    }
  }
}

// Create global instance
window.dataBridge = new DataBridge();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DataBridge;
}

// Initialize DataBridge when script loads
if (typeof window !== 'undefined') {
  window.DataBridge = DataBridge;
  window.dataBridge = new DataBridge();

  // Debug logging
  console.log('DataBridge initialized:', window.dataBridge);
}
