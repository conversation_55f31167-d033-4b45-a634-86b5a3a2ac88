'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  Search, 
  Eye,
  UserCheck,
  UserX,
  Shield,
  User,
  Mail,
  Calendar
} from 'lucide-react'
import { formatDate } from '@/lib/utils'
import toast from 'react-hot-toast'

interface User {
  id: string
  name: string
  email: string
  role: string
  isActive: boolean
  createdAt: string
  lastLoginAt?: string
  profile?: {
    phone?: string
    dateOfBirth?: string
  }
  _count?: {
    orders: number
  }
}

const roleColors = {
  ADMIN: 'bg-red-100 text-red-800',
  USER: 'bg-blue-100 text-blue-800',
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  
  useEffect(() => {
    fetchUsers()
  }, [roleFilter, statusFilter])
  
  const fetchUsers = async () => {
    try {
      const params = new URLSearchParams()
      if (roleFilter !== 'all') {
        params.append('role', roleFilter)
      }
      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }
      
      const response = await fetch(`/api/admin/users?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setUsers(data.data)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      toast.error('Kullanıcılar yüklenirken hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }
  
  const updateUserStatus = async (userId: string, isActive: boolean) => {
    setIsUpdating(true)
    
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        toast.success(`Kullanıcı ${isActive ? 'aktif' : 'pasif'} edildi`)
        fetchUsers()
        setSelectedUser(null)
      } else {
        toast.error(data.error || 'Durum güncellenirken hata oluştu')
      }
    } catch (error) {
      console.error('Error updating user status:', error)
      toast.error('Durum güncellenirken hata oluştu')
    } finally {
      setIsUpdating(false)
    }
  }
  
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Kullanıcı Yönetimi</h1>
          <p className="text-gray-600">Kayıtlı kullanıcıları yönetin</p>
        </div>
      </div>
      
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Kullanıcı adı veya e-posta ile ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Rol filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Roller</SelectItem>
                <SelectItem value="USER">Kullanıcı</SelectItem>
                <SelectItem value="ADMIN">Admin</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Durum filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Durumlar</SelectItem>
                <SelectItem value="active">Aktif</SelectItem>
                <SelectItem value="inactive">Pasif</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      
      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kullanıcılar ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredUsers.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Kullanıcı</TableHead>
                    <TableHead>Rol</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>Sipariş Sayısı</TableHead>
                    <TableHead>Kayıt Tarihi</TableHead>
                    <TableHead>Son Giriş</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={roleColors[user.role as keyof typeof roleColors]}>
                          {user.role === 'ADMIN' ? (
                            <Shield className="h-3 w-3 mr-1" />
                          ) : (
                            <User className="h-3 w-3 mr-1" />
                          )}
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.isActive ? 'default' : 'secondary'}>
                          {user.isActive ? 'Aktif' : 'Pasif'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user._count?.orders || 0}
                      </TableCell>
                      <TableCell>
                        {formatDate(user.createdAt)}
                      </TableCell>
                      <TableCell>
                        {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Hiç'}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedUser(user)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Detay
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <User className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Kullanıcı bulunamadı
              </h3>
              <p className="text-gray-600">
                {searchQuery || roleFilter !== 'all' || statusFilter !== 'all'
                  ? 'Arama kriterlerinize uygun kullanıcı bulunamadı.' 
                  : 'Henüz kayıtlı kullanıcı yok.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* User Detail Modal */}
      <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Kullanıcı Detayı - {selectedUser?.name}
            </DialogTitle>
            <DialogDescription>
              Kullanıcı bilgilerini görüntüleyin ve durumunu güncelleyin
            </DialogDescription>
          </DialogHeader>
          
          {selectedUser && (
            <div className="space-y-6">
              {/* User Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Kişisel Bilgiler</h4>
                  <div className="space-y-2 text-sm">
                    <p><strong>Ad:</strong> {selectedUser.name}</p>
                    <p><strong>E-posta:</strong> {selectedUser.email}</p>
                    <p><strong>Telefon:</strong> {selectedUser.profile?.phone || 'Belirtilmemiş'}</p>
                    <p><strong>Doğum Tarihi:</strong> {selectedUser.profile?.dateOfBirth || 'Belirtilmemiş'}</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Hesap Bilgileri</h4>
                  <div className="space-y-2 text-sm">
                    <p><strong>Rol:</strong> {selectedUser.role}</p>
                    <p><strong>Durum:</strong> {selectedUser.isActive ? 'Aktif' : 'Pasif'}</p>
                    <p><strong>Kayıt Tarihi:</strong> {formatDate(selectedUser.createdAt)}</p>
                    <p><strong>Son Giriş:</strong> {selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : 'Hiç'}</p>
                    <p><strong>Sipariş Sayısı:</strong> {selectedUser._count?.orders || 0}</p>
                  </div>
                </div>
              </div>
              
              {/* Actions */}
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setSelectedUser(null)}
                  disabled={isUpdating}
                >
                  Kapat
                </Button>
                {selectedUser.role !== 'ADMIN' && (
                  <Button
                    variant={selectedUser.isActive ? 'destructive' : 'default'}
                    onClick={() => updateUserStatus(selectedUser.id, !selectedUser.isActive)}
                    disabled={isUpdating}
                  >
                    {selectedUser.isActive ? (
                      <>
                        <UserX className="h-4 w-4 mr-2" />
                        Pasif Et
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-4 w-4 mr-2" />
                        Aktif Et
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
