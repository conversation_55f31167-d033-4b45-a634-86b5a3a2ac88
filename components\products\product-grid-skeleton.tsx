import { Card, CardContent } from '@/components/ui/card'

export function ProductGridSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {[...Array(12)].map((_, index) => (
        <Card key={index} className="overflow-hidden">
          <div className="aspect-square bg-gray-200 animate-pulse" />
          <CardContent className="p-4 space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3" />
            <div className="h-6 bg-gray-200 rounded animate-pulse w-1/2" />
            <div className="h-10 bg-gray-200 rounded animate-pulse w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
