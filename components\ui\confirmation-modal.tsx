'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react'

interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void | Promise<void>
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive' | 'warning' | 'info'
  isLoading?: boolean
}

const variantConfig = {
  default: {
    icon: Info,
    iconColor: 'text-blue-500',
    confirmButtonVariant: 'default' as const,
  },
  destructive: {
    icon: XCircle,
    iconColor: 'text-red-500',
    confirmButtonVariant: 'destructive' as const,
  },
  warning: {
    icon: AlertTriangle,
    iconColor: 'text-yellow-500',
    confirmButtonVariant: 'default' as const,
  },
  info: {
    icon: CheckCircle,
    iconColor: 'text-green-500',
    confirmButtonVariant: 'default' as const,
  },
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Onayla',
  cancelText = 'İptal',
  variant = 'default',
  isLoading = false,
}: ConfirmationModalProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  
  const config = variantConfig[variant]
  const Icon = config.icon
  
  const handleConfirm = async () => {
    setIsProcessing(true)
    try {
      await onConfirm()
      onClose()
    } catch (error) {
      console.error('Confirmation action failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }
  
  const isDisabled = isLoading || isProcessing
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Icon className={`h-6 w-6 ${config.iconColor}`} />
            {title}
          </DialogTitle>
          <DialogDescription className="text-left">
            {description}
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isDisabled}
            className="w-full sm:w-auto"
          >
            {cancelText}
          </Button>
          <Button
            variant={config.confirmButtonVariant}
            onClick={handleConfirm}
            disabled={isDisabled}
            className="w-full sm:w-auto"
          >
            {isProcessing ? 'İşleniyor...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook for easier usage
export function useConfirmationModal() {
  const [modalState, setModalState] = useState<{
    isOpen: boolean
    title: string
    description: string
    onConfirm: () => void | Promise<void>
    variant?: 'default' | 'destructive' | 'warning' | 'info'
    confirmText?: string
    cancelText?: string
  }>({
    isOpen: false,
    title: '',
    description: '',
    onConfirm: () => {},
  })
  
  const showConfirmation = (config: {
    title: string
    description: string
    onConfirm: () => void | Promise<void>
    variant?: 'default' | 'destructive' | 'warning' | 'info'
    confirmText?: string
    cancelText?: string
  }) => {
    setModalState({
      ...config,
      isOpen: true,
    })
  }
  
  const hideConfirmation = () => {
    setModalState(prev => ({ ...prev, isOpen: false }))
  }
  
  const ConfirmationModalComponent = () => (
    <ConfirmationModal
      isOpen={modalState.isOpen}
      onClose={hideConfirmation}
      onConfirm={modalState.onConfirm}
      title={modalState.title}
      description={modalState.description}
      variant={modalState.variant}
      confirmText={modalState.confirmText}
      cancelText={modalState.cancelText}
    />
  )
  
  return {
    showConfirmation,
    hideConfirmation,
    ConfirmationModal: ConfirmationModalComponent,
  }
}
