import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      title,
      firstName,
      lastName,
      phone,
      address,
      district,
      city,
      postalCode,
      isDefault
    } = body
    
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // If this is set as default, unset other default addresses
    if (isDefault) {
      await supabase
        .from('addresses')
        .update({ is_default: false })
        .eq('user_id', user.id)
        .neq('id', params.id)
    }
    
    const { data: updatedAddress, error } = await supabase
      .from('addresses')
      .update({
        title,
        first_name: firstName,
        last_name: lastName,
        phone,
        address,
        district,
        city,
        postal_code: postalCode,
        is_default: isDefault || false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    if (!updatedAddress) {
      return NextResponse.json(
        { success: false, error: 'Adres bulunamadı' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: updatedAddress,
      message: 'Adres başarıyla güncellendi'
    })
    
  } catch (error) {
    console.error('Error updating address:', error)
    return NextResponse.json(
      { success: false, error: 'Adres güncellenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Soft delete - set is_active to false
    const { error } = await supabase
      .from('addresses')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .eq('user_id', user.id)
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Adres başarıyla silindi'
    })
    
  } catch (error) {
    console.error('Error deleting address:', error)
    return NextResponse.json(
      { success: false, error: 'Adres silinirken hata oluştu' },
      { status: 500 }
    )
  }
}
