import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const category = await prisma.category.findUnique({
      where: {
        slug: params.slug,
        isActive: true,
      },
      include: {
        parent: true,
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        products: {
          where: {
            isActive: true,
            status: 'ACTIVE'
          },
          include: {
            images: {
              take: 1,
              orderBy: { sortOrder: 'asc' }
            },
            reviews: {
              select: {
                rating: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 20
        },
        _count: {
          select: {
            products: {
              where: {
                isActive: true,
                status: 'ACTIVE'
              }
            }
          }
        }
      }
    })
    
    if (!category) {
      return NextResponse.json(
        { success: false, error: '<PERSON><PERSON>i bulunamadı' },
        { status: 404 }
      )
    }
    
    // Transform products with average rating
    const transformedCategory = {
      ...category,
      products: category.products.map(product => ({
        ...product,
        averageRating: product.reviews.length > 0 
          ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
          : 0,
        reviewCount: product.reviews.length,
        reviews: undefined,
      }))
    }
    
    return NextResponse.json({
      success: true,
      data: transformedCategory
    })
    
  } catch (error) {
    console.error('Error fetching category:', error)
    return NextResponse.json(
      { success: false, error: 'Kategori yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const body = await request.json()
    
    const category = await prisma.category.update({
      where: { slug: params.slug },
      data: {
        name: body.name,
        slug: body.slug,
        description: body.description,
        image: body.image,
        parentId: body.parentId,
        isActive: body.isActive,
        sortOrder: body.sortOrder,
        updatedAt: new Date(),
      },
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            products: true
          }
        }
      }
    })
    
    return NextResponse.json({
      success: true,
      data: category,
      message: 'Kategori başarıyla güncellendi'
    })
    
  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { success: false, error: 'Kategori güncellenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await prisma.category.update({
      where: { slug: params.slug },
      data: {
        isActive: false,
        updatedAt: new Date(),
      }
    })
    
    return NextResponse.json({
      success: true,
      message: 'Kategori başarıyla silindi'
    })
    
  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { success: false, error: 'Kategori silinirken hata oluştu' },
      { status: 500 }
    )
  }
}
