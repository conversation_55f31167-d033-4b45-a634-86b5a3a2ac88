<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview.html Debug Test - E-Marketing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8">Preview.html Debug Test</h1>
        
        <!-- Connection Status -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">🔗 Bağlantı Durumu</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-medium mb-2">Ana Site (localhost:3000)</h3>
                    <div id="main-site-status" class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                        <span class="text-gray-600">Test ediliyor...</span>
                    </div>
                </div>
                <div>
                    <h3 class="font-medium mb-2">Preview.html</h3>
                    <div id="preview-status" class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-green-600">Aktif</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Button Tests -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">🔘 Buton Testleri</h2>
            <div class="space-y-4">
                <!-- Navigation Buttons -->
                <div>
                    <h3 class="font-medium mb-2">Navigation Butonları</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button onclick="testLink('http://localhost:3000')" class="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">
                            Ana Sayfa
                        </button>
                        <button onclick="testLink('http://localhost:3000/shop')" class="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">
                            Ürünler
                        </button>
                        <button onclick="testLink('http://localhost:3000/categories')" class="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">
                            Kategoriler
                        </button>
                        <button onclick="testLink('http://localhost:3000/about')" class="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700">
                            Hakkımızda
                        </button>
                    </div>
                </div>
                
                <!-- Auth Buttons -->
                <div>
                    <h3 class="font-medium mb-2">Auth Butonları</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        <button onclick="testLink('http://localhost:3000/auth/login')" class="bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700">
                            Giriş Yap
                        </button>
                        <button onclick="testLink('http://localhost:3000/auth/register')" class="bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700">
                            Kayıt Ol
                        </button>
                        <button onclick="testLink('http://localhost:3000/profile/favorites')" class="bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700">
                            Favoriler
                        </button>
                    </div>
                </div>
                
                <!-- Footer Links -->
                <div>
                    <h3 class="font-medium mb-2">Footer Linkleri</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button onclick="testLink('http://localhost:3000/contact')" class="bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                            İletişim
                        </button>
                        <button onclick="testLink('http://localhost:3000/faq')" class="bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                            SSS
                        </button>
                        <button onclick="testLink('http://localhost:3000/privacy')" class="bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                            Gizlilik
                        </button>
                        <button onclick="testLink('http://localhost:3000/terms')" class="bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700">
                            Şartlar
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">📊 Test Sonuçları</h2>
            <div id="test-results" class="space-y-2">
                <p class="text-gray-600">Henüz test yapılmadı. Yukarıdaki butonlara tıklayarak test edin.</p>
            </div>
        </div>
        
        <!-- Console Logs -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">🐛 Console Logları</h2>
            <div id="console-logs" class="bg-gray-100 p-4 rounded text-sm font-mono h-64 overflow-y-auto">
                <p class="text-gray-600">Console logları burada görünecek...</p>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="bg-blue-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">🚀 Hızlı Aksiyonlar</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="openPreview()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Preview.html'i Aç
                </button>
                <button onclick="testAllLinks()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Tüm Linkleri Test Et
                </button>
                <button onclick="clearResults()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Sonuçları Temizle
                </button>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let consoleMessages = [];
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addConsoleMessage('LOG', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addConsoleMessage('ERROR', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addConsoleMessage('WARN', args.join(' '));
        };
        
        function addConsoleMessage(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleMessages.push(`[${timestamp}] ${type}: ${message}`);
            updateConsoleDisplay();
        }
        
        function updateConsoleDisplay() {
            const container = document.getElementById('console-logs');
            container.innerHTML = consoleMessages.join('\n') || 'Henüz log mesajı yok...';
            container.scrollTop = container.scrollHeight;
        }
        
        function testLink(url) {
            const startTime = Date.now();
            
            fetch(url)
                .then(response => {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    const result = {
                        url: url,
                        status: response.status,
                        statusText: response.statusText,
                        duration: duration,
                        success: response.ok,
                        timestamp: new Date().toLocaleTimeString()
                    };
                    
                    testResults.push(result);
                    updateTestResults();
                    
                    // Also try to open the link
                    if (response.ok) {
                        window.open(url, '_blank');
                    }
                })
                .catch(error => {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    const result = {
                        url: url,
                        status: 'ERROR',
                        statusText: error.message,
                        duration: duration,
                        success: false,
                        timestamp: new Date().toLocaleTimeString()
                    };
                    
                    testResults.push(result);
                    updateTestResults();
                });
        }
        
        function updateTestResults() {
            const container = document.getElementById('test-results');
            
            if (testResults.length === 0) {
                container.innerHTML = '<p class="text-gray-600">Henüz test yapılmadı.</p>';
                return;
            }
            
            container.innerHTML = testResults.map(result => `
                <div class="flex items-center justify-between p-3 border rounded ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}">
                    <div class="flex-1">
                        <div class="font-medium ${result.success ? 'text-green-800' : 'text-red-800'}">
                            ${result.url}
                        </div>
                        <div class="text-sm text-gray-600">
                            ${result.timestamp} • ${result.duration}ms • ${result.status} ${result.statusText}
                        </div>
                    </div>
                    <div class="ml-4">
                        <i class="fas ${result.success ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'}"></i>
                    </div>
                </div>
            `).join('');
        }
        
        function checkMainSiteConnection() {
            fetch('http://localhost:3000')
                .then(response => {
                    const statusElement = document.getElementById('main-site-status');
                    if (response.ok) {
                        statusElement.innerHTML = `
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-green-600">Çalışıyor (${response.status})</span>
                        `;
                    } else {
                        statusElement.innerHTML = `
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-yellow-600">Sorunlu (${response.status})</span>
                        `;
                    }
                })
                .catch(error => {
                    const statusElement = document.getElementById('main-site-status');
                    statusElement.innerHTML = `
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span class="text-red-600">Erişilemez</span>
                    `;
                });
        }
        
        function openPreview() {
            window.open('preview.html', '_blank');
        }
        
        function testAllLinks() {
            const links = [
                'http://localhost:3000',
                'http://localhost:3000/shop',
                'http://localhost:3000/categories',
                'http://localhost:3000/about',
                'http://localhost:3000/contact',
                'http://localhost:3000/auth/login',
                'http://localhost:3000/auth/register',
                'http://localhost:3000/profile/favorites',
                'http://localhost:3000/faq',
                'http://localhost:3000/privacy',
                'http://localhost:3000/terms'
            ];
            
            links.forEach((link, index) => {
                setTimeout(() => testLink(link), index * 500);
            });
        }
        
        function clearResults() {
            testResults = [];
            consoleMessages = [];
            updateTestResults();
            updateConsoleDisplay();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkMainSiteConnection();
            
            // Check connection every 30 seconds
            setInterval(checkMainSiteConnection, 30000);
        });
    </script>
</body>
</html>
