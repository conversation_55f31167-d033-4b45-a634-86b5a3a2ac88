'use client'

import { useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Package, Search, Eye, Truck, Clock, CheckCircle, XCircle } from 'lucide-react'
import { useOrderStore } from '@/lib/store/order-store'
import { useAuthStore } from '@/lib/store/auth-store'
import { formatPrice, formatDate } from '@/lib/utils'
import { useRouter } from 'next/navigation'

const statusConfig = {
  PENDING: { label: 'Beklemede', color: 'bg-yellow-500', icon: Clock },
  CONFIRMED: { label: 'Onaylandı', color: 'bg-blue-500', icon: CheckCircle },
  PROCESSING: { label: 'Haz<PERSON>rl<PERSON>ıyor', color: 'bg-purple-500', icon: Package },
  SHIPPED: { label: 'Kargoda', color: 'bg-orange-500', icon: Truck },
  DELIVERED: { label: 'Teslim Edildi', color: 'bg-green-500', icon: CheckCircle },
  CANCELLED: { label: 'İptal Edildi', color: 'bg-red-500', icon: XCircle },
  REFUNDED: { label: 'İade Edildi', color: 'bg-gray-500', icon: XCircle },
}

const paymentStatusConfig = {
  PENDING: { label: 'Beklemede', color: 'bg-yellow-500' },
  PAID: { label: 'Ödendi', color: 'bg-green-500' },
  FAILED: { label: 'Başarısız', color: 'bg-red-500' },
  REFUNDED: { label: 'İade Edildi', color: 'bg-gray-500' },
}

export default function OrdersPage() {
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()
  const {
    orders,
    isLoading,
    filters,
    pagination,
    setFilters,
    fetchOrders,
  } = useOrderStore()
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/orders')
      return
    }
    
    fetchOrders()
  }, [isAuthenticated, router, fetchOrders, filters])
  
  const handleStatusFilter = (status: string) => {
    setFilters({ status: status === 'all' ? undefined : status })
  }
  
  const handleSearch = (search: string) => {
    setFilters({ search: search || undefined })
  }
  
  const handlePageChange = (page: number) => {
    setFilters({ page })
  }
  
  if (!isAuthenticated) {
    return null
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Siparişlerim
          </h1>
          <p className="text-gray-600">
            Tüm siparişlerinizi buradan takip edebilirsiniz
          </p>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Sipariş numarası ara..."
                  className="pl-10"
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
              
              {/* Status Filter */}
              <Select onValueChange={handleStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Durum filtrele" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Durumlar</SelectItem>
                  <SelectItem value="PENDING">Beklemede</SelectItem>
                  <SelectItem value="CONFIRMED">Onaylandı</SelectItem>
                  <SelectItem value="PROCESSING">Hazırlanıyor</SelectItem>
                  <SelectItem value="SHIPPED">Kargoda</SelectItem>
                  <SelectItem value="DELIVERED">Teslim Edildi</SelectItem>
                  <SelectItem value="CANCELLED">İptal Edildi</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Payment Status Filter */}
              <Select onValueChange={(value) => setFilters({ paymentStatus: value === 'all' ? undefined : value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Ödeme durumu" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Ödemeler</SelectItem>
                  <SelectItem value="PENDING">Beklemede</SelectItem>
                  <SelectItem value="PAID">Ödendi</SelectItem>
                  <SelectItem value="FAILED">Başarısız</SelectItem>
                  <SelectItem value="REFUNDED">İade Edildi</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        
        {/* Orders List */}
        {orders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-16">
              <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Henüz siparişiniz yok
              </h3>
              <p className="text-gray-600 mb-6">
                İlk siparişinizi vermek için alışverişe başlayın
              </p>
              <Button asChild>
                <Link href="/shop">Alışverişe Başla</Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => {
              const statusInfo = statusConfig[order.status]
              const paymentInfo = paymentStatusConfig[order.paymentStatus]
              const StatusIcon = statusInfo.icon
              
              return (
                <Card key={order.id} className="overflow-hidden">
                  <CardHeader className="bg-gray-50 border-b">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">
                          Sipariş #{order.orderNumber}
                        </CardTitle>
                        <p className="text-sm text-gray-600">
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={`${statusInfo.color} text-white`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusInfo.label}
                        </Badge>
                        <Badge className={`${paymentInfo.color} text-white`}>
                          {paymentInfo.label}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      {/* Order Items */}
                      <div className="lg:col-span-2">
                        <h4 className="font-medium mb-3">Ürünler</h4>
                        <div className="space-y-3">
                          {order.orderItems?.slice(0, 3).map((item) => (
                            <div key={item.id} className="flex items-center space-x-3">
                              <div className="relative w-12 h-12 flex-shrink-0">
                                <Image
                                  src={item.product?.images?.[0]?.url || '/placeholder-product.jpg'}
                                  alt={item.product?.name || 'Ürün'}
                                  fill
                                  className="object-cover rounded-md"
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {item.product?.name}
                                </p>
                                <p className="text-sm text-gray-600">
                                  {item.quantity} x {formatPrice(item.price)}
                                </p>
                              </div>
                              <span className="text-sm font-medium">
                                {formatPrice(item.total)}
                              </span>
                            </div>
                          ))}
                          {order.orderItems && order.orderItems.length > 3 && (
                            <p className="text-sm text-gray-600">
                              +{order.orderItems.length - 3} ürün daha
                            </p>
                          )}
                        </div>
                      </div>
                      
                      {/* Order Summary */}
                      <div>
                        <h4 className="font-medium mb-3">Sipariş Özeti</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Ara Toplam:</span>
                            <span>{formatPrice(order.totalAmount - order.shippingCost - order.taxAmount)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Kargo:</span>
                            <span>{formatPrice(order.shippingCost)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>KDV:</span>
                            <span>{formatPrice(order.taxAmount)}</span>
                          </div>
                          <div className="flex justify-between font-medium text-base border-t pt-2">
                            <span>Toplam:</span>
                            <span>{formatPrice(order.totalAmount)}</span>
                          </div>
                        </div>
                        
                        <div className="mt-4 space-y-2">
                          <Button variant="outline" size="sm" className="w-full" asChild>
                            <Link href={`/orders/${order.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              Detayları Gör
                            </Link>
                          </Button>
                          
                          {order.shippingInfo?.trackingNumber && (
                            <Button variant="outline" size="sm" className="w-full">
                              <Truck className="h-4 w-4 mr-2" />
                              Kargo Takip
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
        
        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-center space-x-2 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={!pagination.hasPrev}
            >
              Önceki
            </Button>
            
            <div className="flex items-center space-x-1">
              {[...Array(pagination.totalPages)].map((_, index) => {
                const page = index + 1
                const isCurrentPage = page === pagination.page
                
                return (
                  <Button
                    key={page}
                    variant={isCurrentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                    className="min-w-[40px]"
                  >
                    {page}
                  </Button>
                )
              })}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={!pagination.hasNext}
            >
              Sonraki
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
