<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Marketing Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="lib/data-bridge.js"></script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        .active-nav {
            background-color: #f3f4f6;
            border-right: 3px solid #3b82f6;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .transition-all {
            transition: all 0.3s ease;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background-color: white;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-primary:hover {
            background-color: #2563eb;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        .btn-danger {
            background-color: #ef4444;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-danger:hover {
            background-color: #dc2626;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification.success {
            background-color: #10b981;
        }
        .notification.error {
            background-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay (Mobile) -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>
    
    <!-- Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 sidebar-transition">
        <div class="flex items-center justify-between h-16 px-6 border-b">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">A</span>
                </div>
                <span class="text-xl font-bold text-gray-900">Admin Panel</span>
            </div>
            <button id="close-sidebar" class="lg:hidden p-2 rounded-md hover:bg-gray-100">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        
        <nav class="mt-6 px-3">
            <div class="space-y-1">
                <a href="#dashboard" class="nav-item active-nav flex items-center px-3 py-2 text-sm font-medium text-gray-900 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>
                    Dashboard
                </a>
                <a href="#products" class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="package" class="mr-3 h-5 w-5"></i>
                    Ürünler
                </a>
                <a href="#orders" class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="shopping-cart" class="mr-3 h-5 w-5"></i>
                    Siparişler
                </a>
                <a href="#users" class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                    Kullanıcılar
                </a>
                <a href="#reports" class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="bar-chart-3" class="mr-3 h-5 w-5"></i>
                    Raporlar
                </a>
                <a href="#site-management" class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="globe" class="mr-3 h-5 w-5"></i>
                    Site Yönetimi
                </a>
                <a href="#settings" class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                    Ayarlar
                </a>
            </div>
        </nav>
        
        <div class="absolute bottom-0 w-full p-4 border-t">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <i data-lucide="user" class="h-4 w-4 text-gray-600"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">Admin User</p>
                    <p class="text-xs text-gray-500"><EMAIL></p>
                </div>
                <button class="p-1 rounded-md hover:bg-gray-100" onclick="logout()">
                    <i data-lucide="log-out" class="h-4 w-4 text-gray-500"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="lg:pl-64">
        <!-- Top Bar -->
        <div class="sticky top-0 z-40 bg-white shadow-sm border-b">
            <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
                <button id="open-sidebar" class="lg:hidden p-2 rounded-md hover:bg-gray-100">
                    <i data-lucide="menu" class="h-5 w-5"></i>
                </button>
                
                <div class="flex-1"></div>
                
                <div class="flex items-center space-x-4">
                    <a href="/" target="_blank" class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors">
                        <i data-lucide="external-link" class="h-4 w-4 mr-1"></i>
                        Ana Site
                    </a>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Admin
                    </span>
                    <span class="text-sm text-gray-600">
                        Hoş geldin, Admin User
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Page Content -->
        <main class="flex-1 p-4 sm:p-6 lg:p-8">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section">
                <div class="space-y-8">
                    <!-- Page Header -->
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                        <p class="text-gray-600">E-ticaret sitenizin genel durumu</p>
                    </div>
                    
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Toplam Ürün</p>
                                    <p class="text-2xl font-bold text-gray-900">156</p>
                                    <p class="text-xs text-gray-500">Aktif ürün sayısı</p>
                                </div>
                                <div class="p-3 bg-blue-100 rounded-full">
                                    <i data-lucide="package" class="h-6 w-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Toplam Sipariş</p>
                                    <p class="text-2xl font-bold text-gray-900">89</p>
                                    <p class="text-xs text-gray-500">Bu ay</p>
                                </div>
                                <div class="p-3 bg-green-100 rounded-full">
                                    <i data-lucide="shopping-cart" class="h-6 w-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Toplam Kullanıcı</p>
                                    <p class="text-2xl font-bold text-gray-900">234</p>
                                    <p class="text-xs text-gray-500">Kayıtlı kullanıcı</p>
                                </div>
                                <div class="p-3 bg-purple-100 rounded-full">
                                    <i data-lucide="users" class="h-6 w-6 text-purple-600"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Toplam Gelir</p>
                                    <p class="text-2xl font-bold text-gray-900">₺45,678</p>
                                    <p class="text-xs text-gray-500">Bu ay</p>
                                </div>
                                <div class="p-3 bg-yellow-100 rounded-full">
                                    <i data-lucide="dollar-sign" class="h-6 w-6 text-yellow-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Charts Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Satış Grafiği</h3>
                            <canvas id="salesChart" width="400" height="200"></canvas>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Kategori Dağılımı</h3>
                            <canvas id="categoryChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- Recent Orders -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Son Siparişler</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sipariş No</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Müşteri</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tutar</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#ORD-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Ahmet Yılmaz</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Tamamlandı</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₺299.99</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 saat önce</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#ORD-002</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Fatma Demir</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Hazırlanıyor</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₺159.50</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5 saat önce</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div id="products-section" class="section hidden">
                <div class="space-y-6">
                    <!-- Page Header -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Ürün Yönetimi</h1>
                            <p class="text-gray-600">Ürünlerinizi yönetin ve düzenleyin</p>
                        </div>
                        <button onclick="addProduct()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
                            <i data-lucide="plus" class="h-4 w-4"></i>
                            <span>Yeni Ürün</span>
                        </button>
                    </div>

                    <!-- Search and Filters -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="relative flex-1">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"></i>
                                <input type="text" placeholder="Ürün adı veya SKU ile ara..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <select class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>Tüm Kategoriler</option>
                                <option>Elektronik</option>
                                <option>Giyim</option>
                                <option>Ev & Yaşam</option>
                            </select>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- Products will be rendered here by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Orders Section -->
            <div id="orders-section" class="section hidden">
                <div class="space-y-6">
                    <!-- Page Header -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Sipariş Yönetimi</h1>
                            <p class="text-gray-600">Müşteri siparişlerini yönetin ve takip edin</p>
                        </div>
                        <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
                            <i data-lucide="download" class="h-4 w-4"></i>
                            <span>Dışa Aktar</span>
                        </button>
                    </div>

                    <!-- Filters -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="relative flex-1">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"></i>
                                <input type="text" placeholder="Sipariş no, müşteri adı veya e-posta ile ara..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <select class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>Tüm Durumlar</option>
                                <option>Beklemede</option>
                                <option>Hazırlanıyor</option>
                                <option>Kargoda</option>
                                <option>Teslim Edildi</option>
                                <option>İptal Edildi</option>
                            </select>
                            <select class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>Tüm Ödemeler</option>
                                <option>Ödendi</option>
                                <option>Beklemede</option>
                                <option>Başarısız</option>
                            </select>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Siparişler (45)</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sipariş No</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Müşteri</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ödeme</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tutar</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Orders will be rendered here by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Section -->
            <div id="users-section" class="section hidden">
                <div class="space-y-6">
                    <!-- Page Header -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Kullanıcı Yönetimi</h1>
                            <p class="text-gray-600">Kayıtlı kullanıcıları yönetin</p>
                        </div>
                        <button onclick="addUser()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
                            <i data-lucide="user-plus" class="h-4 w-4"></i>
                            <span>Yeni Kullanıcı</span>
                        </button>
                    </div>

                    <!-- Filters -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="relative flex-1">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"></i>
                                <input type="text" placeholder="Kullanıcı adı veya e-posta ile ara..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <select class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>Tüm Roller</option>
                                <option>Kullanıcı</option>
                                <option>Admin</option>
                            </select>
                            <select class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>Tüm Durumlar</option>
                                <option>Aktif</option>
                                <option>Pasif</option>
                            </select>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Kullanıcılar (234)</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kullanıcı</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rol</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sipariş Sayısı</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kayıt Tarihi</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Son Giriş</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Users will be rendered here by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports-section" class="section hidden">
                <div class="space-y-6">
                    <!-- Page Header -->
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Raporlar</h1>
                        <p class="text-gray-600">Satış ve performans raporları</p>
                    </div>

                    <!-- Report Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Satış Raporu</h3>
                                <i data-lucide="trending-up" class="h-6 w-6 text-green-600"></i>
                            </div>
                            <p class="text-gray-600 mb-4">Aylık satış performansı ve trendler</p>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                                Raporu Görüntüle
                            </button>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Ürün Raporu</h3>
                                <i data-lucide="package" class="h-6 w-6 text-blue-600"></i>
                            </div>
                            <p class="text-gray-600 mb-4">En çok satan ürünler ve stok durumu</p>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                                Raporu Görüntüle
                            </button>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6 card-hover transition-all">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Müşteri Raporu</h3>
                                <i data-lucide="users" class="h-6 w-6 text-purple-600"></i>
                            </div>
                            <p class="text-gray-600 mb-4">Müşteri davranışları ve demografik analiz</p>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                                Raporu Görüntüle
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Site Management Section -->
            <div id="site-management-section" class="section hidden">
                <div class="space-y-6">
                    <!-- Page Header -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Site Yönetimi</h1>
                            <p class="text-gray-600">Site içeriklerini ve ayarlarını yönetin</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="previewSite()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
                                <i data-lucide="eye" class="h-4 w-4"></i>
                                <span>Siteyi Görüntüle</span>
                            </button>
                            <button onclick="saveAllChanges()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
                                <i data-lucide="save" class="h-4 w-4"></i>
                                <span>Tüm Değişiklikleri Kaydet</span>
                            </button>
                        </div>
                    </div>

                    <!-- Site Settings -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Site Genel Ayarları</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Site Adı</label>
                                    <input type="text" id="siteName" class="form-input" placeholder="E-Marketing">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Site Açıklaması</label>
                                    <textarea id="siteDescription" rows="3" class="form-input" placeholder="Modern e-ticaret platformu"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Ana Renk</label>
                                    <input type="color" id="primaryColor" class="form-input h-12">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">İkincil Renk</label>
                                    <input type="color" id="secondaryColor" class="form-input h-12">
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">İletişim Bilgileri</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">E-posta</label>
                                    <input type="email" id="contactEmail" class="form-input" placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Telefon</label>
                                    <input type="tel" id="contactPhone" class="form-input" placeholder="(0212) 123 45 67">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Adres</label>
                                    <textarea id="contactAddress" rows="3" class="form-input" placeholder="İstanbul, Türkiye"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Page Content Management -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-900">Sayfa İçerikleri</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-6">
                                <!-- Home Page Content -->
                                <div class="border rounded-lg p-4">
                                    <h4 class="text-md font-semibold text-gray-900 mb-4">Ana Sayfa İçeriği</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Ana Başlık</label>
                                            <input type="text" id="homeHeroTitle" class="form-input" placeholder="En İyi Ürünler, En Uygun Fiyatlar">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Alt Başlık</label>
                                            <input type="text" id="homeHeroSubtitle" class="form-input" placeholder="Kaliteli ürünleri keşfedin">
                                        </div>
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Öne Çıkan Ürünler Başlığı</label>
                                            <input type="text" id="homeFeaturedTitle" class="form-input" placeholder="Öne Çıkan Ürünler">
                                        </div>
                                    </div>
                                </div>

                                <!-- About Page Content -->
                                <div class="border rounded-lg p-4">
                                    <h4 class="text-md font-semibold text-gray-900 mb-4">Hakkımızda Sayfası</h4>
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Sayfa Başlığı</label>
                                            <input type="text" id="aboutTitle" class="form-input" placeholder="Hakkımızda">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Alt Başlık</label>
                                            <textarea id="aboutSubtitle" rows="2" class="form-input" placeholder="Şirket tanıtım metni"></textarea>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Misyonumuz</label>
                                            <textarea id="aboutMission" rows="3" class="form-input" placeholder="Misyon açıklaması"></textarea>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Vizyonumuz</label>
                                            <textarea id="aboutVision" rows="3" class="form-input" placeholder="Vizyon açıklaması"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Social Media -->
                                <div class="border rounded-lg p-4">
                                    <h4 class="text-md font-semibold text-gray-900 mb-4">Sosyal Medya Hesapları</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Facebook</label>
                                            <input type="url" id="socialFacebook" class="form-input" placeholder="https://facebook.com/...">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Twitter</label>
                                            <input type="url" id="socialTwitter" class="form-input" placeholder="https://twitter.com/...">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Instagram</label>
                                            <input type="url" id="socialInstagram" class="form-input" placeholder="https://instagram.com/...">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="section hidden">
                <div class="space-y-6">
                    <!-- Page Header -->
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Ayarlar</h1>
                        <p class="text-gray-600">Sistem ayarlarını yönetin</p>
                    </div>

                    <!-- Settings Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Genel Ayarlar</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Site Adı</label>
                                    <input type="text" value="E-Marketing" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Site Açıklaması</label>
                                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">Modern e-ticaret platformu</textarea>
                                </div>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                                    Kaydet
                                </button>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Ödeme Ayarları</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Kredi Kartı</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Havale/EFT</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Kapıda Ödeme</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                                    Kaydet
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <!-- Product Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 id="productModalTitle" class="text-xl font-bold text-gray-900">Yeni Ürün Ekle</h2>
                    <button onclick="closeModal('productModal')" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-6 w-6"></i>
                    </button>
                </div>
                <form id="productForm">
                    <input type="hidden" id="productId" name="id">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Ürün Adı</label>
                            <input type="text" id="productName" name="name" class="form-input" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                            <input type="text" id="productSku" name="sku" class="form-input" required>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Fiyat (₺)</label>
                                <input type="number" id="productPrice" name="price" class="form-input" step="0.01" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Stok</label>
                                <input type="number" id="productStock" name="stock" class="form-input" required>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                            <select id="productCategory" name="category" class="form-input" required>
                                <option value="">Kategori Seçin</option>
                                <option value="Elektronik">Elektronik</option>
                                <option value="Giyim">Giyim</option>
                                <option value="Ev & Yaşam">Ev & Yaşam</option>
                                <option value="Spor">Spor</option>
                                <option value="Kitap">Kitap</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                            <textarea id="productDescription" name="description" rows="3" class="form-input"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Ürün Resmi URL</label>
                            <input type="url" id="productImage" name="image" class="form-input" placeholder="https://example.com/image.jpg">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="productActive" name="active" class="mr-2">
                            <label for="productActive" class="text-sm font-medium text-gray-700">Aktif</label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeModal('productModal')" class="btn-secondary">İptal</button>
                        <button type="submit" class="btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 id="userModalTitle" class="text-xl font-bold text-gray-900">Yeni Kullanıcı Ekle</h2>
                    <button onclick="closeModal('userModal')" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-6 w-6"></i>
                    </button>
                </div>
                <form id="userForm">
                    <input type="hidden" id="userId" name="id">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Ad Soyad</label>
                            <input type="text" id="userName" name="name" class="form-input" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">E-posta</label>
                            <input type="email" id="userEmail" name="email" class="form-input" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Şifre</label>
                            <input type="password" id="userPassword" name="password" class="form-input" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Rol</label>
                            <select id="userRole" name="role" class="form-input" required>
                                <option value="USER">Kullanıcı</option>
                                <option value="ADMIN">Admin</option>
                            </select>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="userActive" name="active" class="mr-2" checked>
                            <label for="userActive" class="text-sm font-medium text-gray-700">Aktif</label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeModal('userModal')" class="btn-secondary">İptal</button>
                        <button type="submit" class="btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Order Detail Modal -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-900">Sipariş Detayı</h2>
                    <button onclick="closeModal('orderModal')" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-6 w-6"></i>
                    </button>
                </div>
                <div id="orderDetails">
                    <!-- Order details will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-900">Silme Onayı</h2>
                    <button onclick="closeModal('deleteModal')" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-6 w-6"></i>
                    </button>
                </div>
                <p class="text-gray-600 mb-6">Bu işlemi geri alamazsınız. Silmek istediğinizden emin misiniz?</p>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('deleteModal')" class="btn-secondary">İptal</button>
                    <button type="button" id="confirmDeleteBtn" class="btn-danger">Sil</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notificationText"></span>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Data Storage - Using DataBridge for real-time sync
        let products = [];
        let users = [];
        let orders = [];
        let categories = [];
        let siteSettings = {};
        let pageContents = {};

        // Initialize data from DataBridge
        function initializeData() {
            products = window.dataBridge.getProducts();
            users = window.dataBridge.getUsers();
            orders = window.dataBridge.getOrders();
            categories = window.dataBridge.getData()?.categories || [];
            siteSettings = window.dataBridge.getSiteSettings();
            pageContents = window.dataBridge.getData()?.pageContents || {};
        }

        // Listen for data changes from other windows/tabs
        window.addEventListener('databridge:change', (event) => {
            initializeData();
            refreshAllViews();
        });

        function refreshAllViews() {
            renderProducts();
            renderUsers();
            renderOrders();
            updateDashboardStats();
            renderSiteManagement();
        }

        // Site Management Functions
        function renderSiteManagement() {
            const settings = window.dataBridge.getSiteSettings();
            const homeContent = window.dataBridge.getPageContent('home');
            const aboutContent = window.dataBridge.getPageContent('about');

            // Site Settings
            document.getElementById('siteName').value = settings.siteName || '';
            document.getElementById('siteDescription').value = settings.siteDescription || '';
            document.getElementById('primaryColor').value = settings.primaryColor || '#3b82f6';
            document.getElementById('secondaryColor').value = settings.secondaryColor || '#8b5cf6';
            document.getElementById('contactEmail').value = settings.contactEmail || '';
            document.getElementById('contactPhone').value = settings.contactPhone || '';
            document.getElementById('contactAddress').value = settings.address || '';

            // Home Page Content
            document.getElementById('homeHeroTitle').value = homeContent.heroTitle || '';
            document.getElementById('homeHeroSubtitle').value = homeContent.heroSubtitle || '';
            document.getElementById('homeFeaturedTitle').value = homeContent.featuredTitle || '';

            // About Page Content
            document.getElementById('aboutTitle').value = aboutContent.title || '';
            document.getElementById('aboutSubtitle').value = aboutContent.subtitle || '';
            document.getElementById('aboutMission').value = aboutContent.mission || '';
            document.getElementById('aboutVision').value = aboutContent.vision || '';

            // Social Media
            const social = settings.socialMedia || {};
            document.getElementById('socialFacebook').value = social.facebook || '';
            document.getElementById('socialTwitter').value = social.twitter || '';
            document.getElementById('socialInstagram').value = social.instagram || '';
        }

        function saveAllChanges() {
            // Collect all form data
            const siteSettings = {
                siteName: document.getElementById('siteName').value,
                siteDescription: document.getElementById('siteDescription').value,
                primaryColor: document.getElementById('primaryColor').value,
                secondaryColor: document.getElementById('secondaryColor').value,
                contactEmail: document.getElementById('contactEmail').value,
                contactPhone: document.getElementById('contactPhone').value,
                address: document.getElementById('contactAddress').value,
                socialMedia: {
                    facebook: document.getElementById('socialFacebook').value,
                    twitter: document.getElementById('socialTwitter').value,
                    instagram: document.getElementById('socialInstagram').value
                }
            };

            const homeContent = {
                heroTitle: document.getElementById('homeHeroTitle').value,
                heroSubtitle: document.getElementById('homeHeroSubtitle').value,
                featuredTitle: document.getElementById('homeFeaturedTitle').value
            };

            const aboutContent = {
                title: document.getElementById('aboutTitle').value,
                subtitle: document.getElementById('aboutSubtitle').value,
                mission: document.getElementById('aboutMission').value,
                vision: document.getElementById('aboutVision').value
            };

            // Save to DataBridge
            const success1 = window.dataBridge.updateSiteSettings(siteSettings);
            const success2 = window.dataBridge.updatePageContent('home', homeContent);
            const success3 = window.dataBridge.updatePageContent('about', aboutContent);

            if (success1 && success2 && success3) {
                showNotification('Tüm değişiklikler başarıyla kaydedildi!');
            } else {
                showNotification('Değişiklikler kaydedilirken hata oluştu!', 'error');
            }
        }

        function previewSite() {
            // Open main site in new tab
            const mainSiteUrl = window.location.origin + '/';
            window.open(mainSiteUrl, '_blank');
        }

        // Utility Functions
        function formatPrice(price) {
            return window.dataBridge.formatPrice(price);
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            const notificationText = document.getElementById('notificationText');

            notificationText.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.add('show');
            lucide.createIcons();
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('show');
        }

        // Product Management Functions
        function renderProducts(productsToRender = products) {
            const container = document.querySelector('#products-section .grid');
            if (!container) return;

            container.innerHTML = productsToRender.map(product => {
                const imageUrl = product.images && product.images.length > 0 ? product.images[0].url : 'https://via.placeholder.com/300x300?text=' + encodeURIComponent(product.name);
                const categoryName = product.category ? product.category.name : 'Kategori Yok';

                return `
                <div class="bg-white rounded-lg shadow overflow-hidden card-hover transition-all">
                    <div class="relative aspect-square">
                        <img src="${imageUrl}" alt="${product.name}" class="w-full h-full object-cover">
                        ${product.stock < 10 ? '<span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs rounded">Düşük Stok</span>' : ''}
                        ${!product.isActive ? '<span class="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 text-xs rounded">Pasif</span>' : ''}
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-1">${product.name}</h3>
                        <p class="text-sm text-gray-600 mb-2">SKU: ${product.sku || 'N/A'}</p>
                        <p class="text-xs text-gray-500 mb-2">${categoryName}</p>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-lg font-bold text-gray-900">${formatPrice(product.price)}</span>
                            <span class="text-sm text-gray-600">Stok: ${product.stock}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewProduct('${product.id}')" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm transition-colors">
                                <i data-lucide="eye" class="h-3 w-3 inline mr-1"></i>
                                Görüntüle
                            </button>
                            <button onclick="editProduct('${product.id}')" class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded text-sm transition-colors">
                                <i data-lucide="edit" class="h-3 w-3"></i>
                            </button>
                            <button onclick="deleteProduct('${product.id}')" class="bg-red-100 hover:bg-red-200 text-red-700 px-3 py-2 rounded text-sm transition-colors">
                                <i data-lucide="trash-2" class="h-3 w-3"></i>
                            </button>
                        </div>
                    </div>
                </div>
                `;
            }).join('');

            lucide.createIcons();
        }

        function addProduct() {
            document.getElementById('productModalTitle').textContent = 'Yeni Ürün Ekle';
            document.getElementById('productForm').reset();
            document.getElementById('productId').value = '';
            document.getElementById('productActive').checked = true;

            // Populate category dropdown
            populateCategoryDropdown();

            openModal('productModal');
        }

        function editProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            document.getElementById('productModalTitle').textContent = 'Ürün Düzenle';
            document.getElementById('productId').value = product.id;
            document.getElementById('productName').value = product.name;
            document.getElementById('productSku').value = product.sku || '';
            document.getElementById('productPrice').value = product.price;
            document.getElementById('productStock').value = product.stock;
            document.getElementById('productDescription').value = product.description || '';
            document.getElementById('productImage').value = product.images && product.images.length > 0 ? product.images[0].url : '';
            document.getElementById('productActive').checked = product.isActive;

            // Populate category dropdown and select current category
            populateCategoryDropdown();
            document.getElementById('productCategory').value = product.categoryId || '';

            openModal('productModal');
        }

        function populateCategoryDropdown() {
            const categorySelect = document.getElementById('productCategory');
            categorySelect.innerHTML = '<option value="">Kategori Seçin</option>';

            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        }

        function viewProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            const categoryName = product.category ? product.category.name : 'Kategori Yok';
            const imageUrl = product.images && product.images.length > 0 ? product.images[0].url : 'Resim Yok';

            alert(`Ürün Detayı:\n\nAd: ${product.name}\nSKU: ${product.sku || 'N/A'}\nFiyat: ${formatPrice(product.price)}\nStok: ${product.stock}\nKategori: ${categoryName}\nDurum: ${product.isActive ? 'Aktif' : 'Pasif'}\nAçıklama: ${product.description || 'Açıklama yok'}\nResim: ${imageUrl}`);
        }

        function deleteProduct(id) {
            const product = products.find(p => p.id === id);
            if (!product) return;

            document.getElementById('confirmDeleteBtn').onclick = () => {
                if (window.dataBridge.deleteProduct(id)) {
                    products = window.dataBridge.getProducts();
                    renderProducts();
                    updateDashboardStats();
                    closeModal('deleteModal');
                    showNotification(`${product.name} başarıyla silindi!`);
                } else {
                    showNotification('Ürün silinirken hata oluştu!', 'error');
                }
            };

            openModal('deleteModal');
        }

        function searchProducts() {
            const searchTerm = document.querySelector('#products-section input[type="text"]').value.toLowerCase();
            const categoryFilter = document.querySelector('#products-section select').value;

            let filtered = products.filter(product => {
                const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                                    product.sku.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || categoryFilter === 'Tüm Kategoriler' ||
                                      product.category === categoryFilter;

                return matchesSearch && matchesCategory;
            });

            renderProducts(filtered);
        }

        // User Management Functions
        function renderUsers(usersToRender = users) {
            const tbody = document.querySelector('#users-section tbody');
            if (!tbody) return;

            tbody.innerHTML = usersToRender.map(user => {
                const createdDate = window.dataBridge.formatDate(user.createdAt);
                const lastLogin = user.lastLoginAt ? window.dataBridge.getRelativeTime(user.lastLoginAt) : 'Hiç';

                return `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                <i data-lucide="user" class="h-4 w-4 text-gray-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">${user.name}</p>
                                <p class="text-sm text-gray-600">${user.email}</p>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}">${user.role}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">${user.isActive ? 'Aktif' : 'Pasif'}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.orderCount}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${createdDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${lastLogin}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewUser('${user.id}')" class="text-blue-600 hover:text-blue-900">
                            <i data-lucide="eye" class="h-3 w-3 inline mr-1"></i>
                            Detay
                        </button>
                    </td>
                </tr>
                `;
            }).join('');

            lucide.createIcons();
        }

        function addUser() {
            document.getElementById('userModalTitle').textContent = 'Yeni Kullanıcı Ekle';
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('userActive').checked = true;
            document.getElementById('userRole').value = 'USER';
            openModal('userModal');
        }

        function viewUser(id) {
            const user = users.find(u => u.id === id);
            if (!user) return;

            alert(`Kullanıcı Detayı:\n\nAd: ${user.name}\nE-posta: ${user.email}\nRol: ${user.role}\nDurum: ${user.active ? 'Aktif' : 'Pasif'}\nSipariş Sayısı: ${user.orderCount}\nKayıt Tarihi: ${user.createdAt}\nSon Giriş: ${user.lastLogin}`);
        }

        function searchUsers() {
            const searchTerm = document.querySelector('#users-section input[type="text"]').value.toLowerCase();
            const roleFilter = document.querySelector('#users-section select:nth-of-type(1)').value;
            const statusFilter = document.querySelector('#users-section select:nth-of-type(2)').value;

            let filtered = users.filter(user => {
                const matchesSearch = user.name.toLowerCase().includes(searchTerm) ||
                                    user.email.toLowerCase().includes(searchTerm);
                const matchesRole = !roleFilter || roleFilter === 'Tüm Roller' ||
                                  user.role === roleFilter;
                const matchesStatus = !statusFilter || statusFilter === 'Tüm Durumlar' ||
                                    (statusFilter === 'Aktif' && user.active) ||
                                    (statusFilter === 'Pasif' && !user.active);

                return matchesSearch && matchesRole && matchesStatus;
            });

            renderUsers(filtered);
        }

        // Order Management Functions
        function renderOrders(ordersToRender = orders) {
            const tbody = document.querySelector('#orders-section tbody');
            if (!tbody) return;

            tbody.innerHTML = ordersToRender.map(order => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#${order.orderNumber}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <p class="font-medium text-gray-900">${order.customer.name}</p>
                            <p class="text-sm text-gray-600">${order.customer.email}</p>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}">${order.status}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(order.paymentStatus)}">${order.paymentStatus}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatPrice(order.total)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.date}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewOrder(${order.id})" class="text-blue-600 hover:text-blue-900 mr-3">
                            <i data-lucide="eye" class="h-4 w-4 inline mr-1"></i>
                            Detay
                        </button>
                    </td>
                </tr>
            `).join('');

            lucide.createIcons();
        }

        function getStatusColor(status) {
            switch(status) {
                case 'Teslim Edildi': return 'bg-green-100 text-green-800';
                case 'Kargoda': return 'bg-blue-100 text-blue-800';
                case 'Hazırlanıyor': return 'bg-yellow-100 text-yellow-800';
                case 'Beklemede': return 'bg-gray-100 text-gray-800';
                case 'İptal Edildi': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getPaymentStatusColor(status) {
            switch(status) {
                case 'Ödendi': return 'bg-green-100 text-green-800';
                case 'Beklemede': return 'bg-yellow-100 text-yellow-800';
                case 'Başarısız': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function viewOrder(id) {
            const order = orders.find(o => o.id === id);
            if (!order) return;

            const orderDetails = document.getElementById('orderDetails');
            orderDetails.innerHTML = `
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Sipariş Bilgileri</h4>
                            <p><strong>Sipariş No:</strong> #${order.orderNumber}</p>
                            <p><strong>Tarih:</strong> ${order.date}</p>
                            <p><strong>Durum:</strong> <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}">${order.status}</span></p>
                            <p><strong>Ödeme:</strong> <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(order.paymentStatus)}">${order.paymentStatus}</span></p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Müşteri Bilgileri</h4>
                            <p><strong>Ad:</strong> ${order.customer.name}</p>
                            <p><strong>E-posta:</strong> ${order.customer.email}</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold text-gray-900 mb-2">Sipariş Kalemleri</h4>
                        <div class="border rounded-lg overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Ürün</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Adet</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Fiyat</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    ${order.items.map(item => `
                                        <tr>
                                            <td class="px-4 py-2 text-sm text-gray-900">${item.name}</td>
                                            <td class="px-4 py-2 text-sm text-gray-900">${item.quantity}</td>
                                            <td class="px-4 py-2 text-sm text-gray-900">${formatPrice(item.price)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 text-right">
                            <p class="text-lg font-bold">Toplam: ${formatPrice(order.total)}</p>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <select id="orderStatusSelect" class="form-input" style="width: auto;">
                            <option value="Beklemede" ${order.status === 'Beklemede' ? 'selected' : ''}>Beklemede</option>
                            <option value="Hazırlanıyor" ${order.status === 'Hazırlanıyor' ? 'selected' : ''}>Hazırlanıyor</option>
                            <option value="Kargoda" ${order.status === 'Kargoda' ? 'selected' : ''}>Kargoda</option>
                            <option value="Teslim Edildi" ${order.status === 'Teslim Edildi' ? 'selected' : ''}>Teslim Edildi</option>
                            <option value="İptal Edildi" ${order.status === 'İptal Edildi' ? 'selected' : ''}>İptal Edildi</option>
                        </select>
                        <button onclick="updateOrderStatus(${order.id})" class="btn-primary">Durumu Güncelle</button>
                    </div>
                </div>
            `;

            openModal('orderModal');
        }

        function updateOrderStatus(id) {
            const newStatus = document.getElementById('orderStatusSelect').value;

            // Map status to Turkish
            const statusMap = {
                'PENDING': 'Beklemede',
                'PROCESSING': 'Hazırlanıyor',
                'SHIPPED': 'Kargoda',
                'DELIVERED': 'Teslim Edildi',
                'CANCELLED': 'İptal Edildi'
            };

            const turkishStatus = Object.keys(statusMap).find(key => statusMap[key] === newStatus) || newStatus;

            if (window.dataBridge.updateOrderStatus(id, turkishStatus)) {
                orders = window.dataBridge.getOrders();
                renderOrders();
                closeModal('orderModal');
                showNotification(`Sipariş durumu "${newStatus}" olarak güncellendi!`);
            } else {
                showNotification('Sipariş durumu güncellenirken hata oluştu!', 'error');
            }
        }

        function searchOrders() {
            const searchTerm = document.querySelector('#orders-section input[type="text"]').value.toLowerCase();
            const statusFilter = document.querySelector('#orders-section select:nth-of-type(1)').value;
            const paymentFilter = document.querySelector('#orders-section select:nth-of-type(2)').value;

            let filtered = orders.filter(order => {
                const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm) ||
                                    order.customer.name.toLowerCase().includes(searchTerm) ||
                                    order.customer.email.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || statusFilter === 'Tüm Durumlar' ||
                                    order.status === statusFilter;
                const matchesPayment = !paymentFilter || paymentFilter === 'Tüm Ödemeler' ||
                                     order.paymentStatus === paymentFilter;

                return matchesSearch && matchesStatus && matchesPayment;
            });

            renderOrders(filtered);
        }

        // Form Handling
        function handleProductForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const categoryId = formData.get('category');
            const category = categories.find(c => c.id === categoryId);

            const productData = {
                name: formData.get('name'),
                sku: formData.get('sku'),
                price: parseFloat(formData.get('price')),
                stock: parseInt(formData.get('stock')),
                categoryId: categoryId,
                category: category,
                description: formData.get('description'),
                isActive: formData.get('active') === 'on',
                isFeatured: false,
                status: 'ACTIVE',
                images: formData.get('image') ? [{
                    id: window.dataBridge.generateId(),
                    url: formData.get('image'),
                    alt: formData.get('name'),
                    sortOrder: 0
                }] : []
            };

            const productId = formData.get('id');

            if (productId) {
                // Update existing product
                if (window.dataBridge.updateProduct(productId, productData)) {
                    products = window.dataBridge.getProducts();
                    showNotification('Ürün başarıyla güncellendi!');
                } else {
                    showNotification('Ürün güncellenirken hata oluştu!', 'error');
                }
            } else {
                // Add new product
                if (window.dataBridge.addProduct(productData)) {
                    products = window.dataBridge.getProducts();
                    showNotification('Yeni ürün başarıyla eklendi!');
                } else {
                    showNotification('Ürün eklenirken hata oluştu!', 'error');
                }
            }

            renderProducts();
            updateDashboardStats();
            closeModal('productModal');
        }

        function handleUserForm(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const userData = {
                name: formData.get('name'),
                email: formData.get('email'),
                role: formData.get('role'),
                isActive: formData.get('active') === 'on'
            };

            const userId = formData.get('id');

            if (userId) {
                // Update existing user (not implemented in DataBridge yet)
                showNotification('Kullanıcı güncelleme özelliği yakında eklenecek!', 'error');
            } else {
                // Add new user
                if (window.dataBridge.addUser(userData)) {
                    users = window.dataBridge.getUsers();
                    showNotification('Yeni kullanıcı başarıyla eklendi!');
                } else {
                    showNotification('Kullanıcı eklenirken hata oluştu!', 'error');
                }
            }

            renderUsers();
            updateDashboardStats();
            closeModal('userModal');
        }

        // Dashboard Functions
        function updateDashboardStats() {
            // Update stats cards
            document.querySelector('#dashboard-section .grid .bg-white:nth-child(1) .text-2xl').textContent = products.length;
            document.querySelector('#dashboard-section .grid .bg-white:nth-child(2) .text-2xl').textContent = orders.length;
            document.querySelector('#dashboard-section .grid .bg-white:nth-child(3) .text-2xl').textContent = users.length;

            const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
            document.querySelector('#dashboard-section .grid .bg-white:nth-child(4) .text-2xl').textContent = formatPrice(totalRevenue);

            // Update recent orders table
            const recentOrdersTable = document.querySelector('#dashboard-section tbody');
            if (recentOrdersTable) {
                recentOrdersTable.innerHTML = orders.slice(0, 5).map(order => `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#${order.orderNumber}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${order.customer.name}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}">${order.status}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatPrice(order.total)}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${order.date}</td>
                    </tr>
                `).join('');
            }
        }

        // Sidebar functionality
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        const openSidebarBtn = document.getElementById('open-sidebar');
        const closeSidebarBtn = document.getElementById('close-sidebar');

        function openSidebar() {
            sidebar.classList.remove('-translate-x-full');
            sidebarOverlay.classList.remove('hidden');
        }

        function closeSidebar() {
            sidebar.classList.add('-translate-x-full');
            sidebarOverlay.classList.add('hidden');
        }

        openSidebarBtn.addEventListener('click', openSidebar);
        closeSidebarBtn.addEventListener('click', closeSidebar);
        sidebarOverlay.addEventListener('click', closeSidebar);

        // Navigation functionality
        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('.section');

        function showSection(sectionId) {
            // Hide all sections
            sections.forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionId + '-section');
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }

            // Update active nav item
            navItems.forEach(item => {
                item.classList.remove('active-nav');
                item.classList.add('text-gray-700');
                item.classList.remove('text-gray-900');
            });

            const activeItem = document.querySelector(`[href="#${sectionId}"]`);
            if (activeItem) {
                activeItem.classList.add('active-nav');
                activeItem.classList.remove('text-gray-700');
                activeItem.classList.add('text-gray-900');
            }

            // Close sidebar on mobile after navigation
            if (window.innerWidth < 1024) {
                closeSidebar();
            }
        }

        // Add click event listeners to nav items
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const href = item.getAttribute('href');
                const sectionId = href.substring(1); // Remove the '#'
                showSection(sectionId);
            });
        });

        // Initialize charts
        function initializeCharts() {
            // Sales Chart
            const salesCtx = document.getElementById('salesChart');
            if (salesCtx) {
                new Chart(salesCtx, {
                    type: 'line',
                    data: {
                        labels: ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'],
                        datasets: [{
                            label: 'Satışlar (₺)',
                            data: [12000, 19000, 15000, 25000, 22000, 30000],
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '₺' + value.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Category Chart
            const categoryCtx = document.getElementById('categoryChart');
            if (categoryCtx) {
                new Chart(categoryCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Elektronik', 'Giyim', 'Ev & Yaşam', 'Spor', 'Kitap'],
                        datasets: [{
                            data: [35, 25, 20, 15, 5],
                            backgroundColor: [
                                'rgb(59, 130, 246)',
                                'rgb(16, 185, 129)',
                                'rgb(245, 158, 11)',
                                'rgb(239, 68, 68)',
                                'rgb(139, 92, 246)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // Logout function
        function logout() {
            if (confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
                alert('Çıkış yapıldı!');
                // Here you would typically redirect to login page
                // window.location.href = '/login';
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize data from DataBridge
            initializeData();

            // Show dashboard by default
            showSection('dashboard');

            // Initialize charts
            setTimeout(initializeCharts, 100);

            // Initialize data rendering
            renderProducts();
            renderUsers();
            renderOrders();
            updateDashboardStats();
            renderSiteManagement();

            // Add form event listeners
            document.getElementById('productForm').addEventListener('submit', handleProductForm);
            document.getElementById('userForm').addEventListener('submit', handleUserForm);

            // Add search event listeners
            const productSearch = document.querySelector('#products-section input[type="text"]');
            const productCategoryFilter = document.querySelector('#products-section select');
            if (productSearch) {
                productSearch.addEventListener('input', searchProducts);
            }
            if (productCategoryFilter) {
                productCategoryFilter.addEventListener('change', searchProducts);
            }

            const userSearch = document.querySelector('#users-section input[type="text"]');
            const userRoleFilter = document.querySelector('#users-section select:nth-of-type(1)');
            const userStatusFilter = document.querySelector('#users-section select:nth-of-type(2)');
            if (userSearch) {
                userSearch.addEventListener('input', searchUsers);
            }
            if (userRoleFilter) {
                userRoleFilter.addEventListener('change', searchUsers);
            }
            if (userStatusFilter) {
                userStatusFilter.addEventListener('change', searchUsers);
            }

            const orderSearch = document.querySelector('#orders-section input[type="text"]');
            const orderStatusFilter = document.querySelector('#orders-section select:nth-of-type(1)');
            const orderPaymentFilter = document.querySelector('#orders-section select:nth-of-type(2)');
            if (orderSearch) {
                orderSearch.addEventListener('input', searchOrders);
            }
            if (orderStatusFilter) {
                orderStatusFilter.addEventListener('change', searchOrders);
            }
            if (orderPaymentFilter) {
                orderPaymentFilter.addEventListener('change', searchOrders);
            }

            // Re-initialize icons after dynamic content changes
            lucide.createIcons();
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) {
                closeSidebar();
            }
        });
    </script>
</body>
</html>
