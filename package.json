{"name": "e-marketing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup:admin": "node scripts/create-admin.js", "test:admin": "echo 'Admin panel test rehberi için ADMIN_PANEL_TEST_GUIDE.md dosyasını inceleyin'"}, "dependencies": {"next": "15.1.3", "react": "19.0.0", "react-dom": "19.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5", "tailwindcss": "^3.4.1", "autoprefixer": "^10.0.1", "postcss": "^8", "@tailwindcss/forms": "^0.5.7", "@supabase/supabase-js": "^2.39.3", "@supabase/auth-helpers-nextjs": "^0.8.7", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "stripe": "^14.12.0", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "lucide-react": "^0.303.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "react-hot-toast": "^2.4.1", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.2.0", "@uploadthing/react": "^6.2.0", "resend": "^3.2.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "15.1.3", "@types/uuid": "^9.0.7", "uuid": "^9.0.1"}}