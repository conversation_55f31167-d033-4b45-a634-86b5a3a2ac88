import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Yetkisiz erişim' },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    const { isActive, role } = body
    
    // Prevent admin from deactivating themselves
    if (params.id === user.id && isActive === false) {
      return NextResponse.json(
        { success: false, error: '<PERSON><PERSON> pasif edemezsiniz' },
        { status: 400 }
      )
    }
    
    // Get target user
    const { data: targetUser } = await supabase
      .from('users')
      .select('role')
      .eq('id', params.id)
      .single()
    
    if (!targetUser) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı bulunamadı' },
        { status: 404 }
      )
    }
    
    // Prevent changing admin role
    if (targetUser.role === 'ADMIN' && role && role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin rolü değiştirilemez' },
        { status: 400 }
      )
    }
    
    const updateData: any = {
      updated_at: new Date().toISOString()
    }
    
    if (typeof isActive === 'boolean') {
      updateData.is_active = isActive
    }
    
    if (role && targetUser.role !== 'ADMIN') {
      updateData.role = role
    }
    
    const { data: updatedUser, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', params.id)
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'Kullanıcı başarıyla güncellendi'
    })
    
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { success: false, error: 'Kullanıcı güncellenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Yetkisiz erişim' },
        { status: 403 }
      )
    }
    
    const { data: targetUser, error } = await supabase
      .from('users')
      .select(`
        *,
        profile:profiles(*),
        orders:orders(count)
      `)
      .eq('id', params.id)
      .single()
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    if (!targetUser) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı bulunamadı' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: targetUser
    })
    
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { success: false, error: 'Kullanıcı yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}
