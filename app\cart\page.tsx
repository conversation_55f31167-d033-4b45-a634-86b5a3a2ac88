'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  ArrowLeft,
  ShoppingBag,
  Percent,
  CreditCard
} from 'lucide-react'
import { useCartStore } from '@/lib/store/cart-store'
import { useAuthStore } from '@/lib/store/auth-store'
import { formatPrice } from '@/lib/utils'
import toast from 'react-hot-toast'

export default function CartPage() {
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()
  const { 
    items, 
    totalItems, 
    totalPrice, 
    updateQuantity, 
    removeItem, 
    clearCart,
    applyCoupon,
    removeCoupon,
    coupon,
    discountAmount,
    finalPrice
  } = useCartStore()
  
  const [couponCode, setCouponCode] = useState('')
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false)
  
  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(itemId)
    } else {
      updateQuantity(itemId, newQuantity)
    }
  }
  
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('Lütfen kupon kodunu girin')
      return
    }
    
    setIsApplyingCoupon(true)
    
    try {
      const success = await applyCoupon(couponCode)
      if (success) {
        toast.success('Kupon başarıyla uygulandı!')
        setCouponCode('')
      } else {
        toast.error('Geçersiz kupon kodu')
      }
    } catch (error) {
      toast.error('Kupon uygulanırken hata oluştu')
    } finally {
      setIsApplyingCoupon(false)
    }
  }
  
  const handleCheckout = () => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/checkout')
    } else {
      router.push('/checkout')
    }
  }
  
  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="flex items-center gap-4 mb-8">
              <Button variant="ghost" size="icon" onClick={() => router.back()}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className="text-2xl font-bold">Sepetim</h1>
            </div>
            
            {/* Empty Cart */}
            <Card>
              <CardContent className="text-center py-16">
                <ShoppingCart className="h-16 w-16 text-gray-300 mx-auto mb-6" />
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Sepetiniz boş
                </h2>
                <p className="text-gray-600 mb-8">
                  Alışverişe başlamak için ürünlerimizi keşfedin
                </p>
                <Button asChild>
                  <Link href="/shop">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Alışverişe Başla
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" size="icon" onClick={() => router.back()}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">Sepetim</h1>
            <Badge variant="secondary">
              {totalItems} ürün
            </Badge>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Sepetinizdeki Ürünler</CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={clearCart}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Sepeti Temizle
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                        {/* Product Image */}
                        <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
                          <Image
                            src={item.product.images?.[0]?.url || '/placeholder-product.jpg'}
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        
                        {/* Product Info */}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 truncate">
                            {item.product.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            SKU: {item.product.sku}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="font-semibold text-primary">
                              {formatPrice(item.product.price)}
                            </span>
                            {item.product.comparePrice && (
                              <span className="text-sm text-gray-500 line-through">
                                {formatPrice(item.product.comparePrice)}
                              </span>
                            )}
                          </div>
                        </div>
                        
                        {/* Quantity Controls */}
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-12 text-center font-medium">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        {/* Item Total */}
                        <div className="text-right">
                          <div className="font-semibold">
                            {formatPrice(item.product.price * item.quantity)}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(item.id)}
                            className="text-red-600 hover:text-red-700 mt-1"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="space-y-6">
                {/* Coupon */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Percent className="h-5 w-5 mr-2" />
                      İndirim Kuponu
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {coupon ? (
                      <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div>
                          <div className="font-medium text-green-800">
                            {coupon.code}
                          </div>
                          <div className="text-sm text-green-600">
                            %{coupon.discount} indirim
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={removeCoupon}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <Input
                          placeholder="Kupon kodunu girin"
                          value={couponCode}
                          onChange={(e) => setCouponCode(e.target.value)}
                        />
                        <Button
                          onClick={handleApplyCoupon}
                          disabled={isApplyingCoupon}
                          className="w-full"
                          variant="outline"
                        >
                          {isApplyingCoupon ? 'Uygulanıyor...' : 'Kuponu Uygula'}
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {/* Order Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Sipariş Özeti</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span>Ara Toplam</span>
                      <span>{formatPrice(totalPrice)}</span>
                    </div>
                    
                    {discountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>İndirim</span>
                        <span>-{formatPrice(discountAmount)}</span>
                      </div>
                    )}
                    
                    <div className="flex justify-between">
                      <span>Kargo</span>
                      <span className="text-green-600">Ücretsiz</span>
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between text-lg font-semibold">
                      <span>Toplam</span>
                      <span>{formatPrice(finalPrice)}</span>
                    </div>
                    
                    <Button 
                      onClick={handleCheckout}
                      className="w-full"
                      size="lg"
                    >
                      <CreditCard className="h-4 w-4 mr-2" />
                      Ödemeye Geç
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="w-full" 
                      asChild
                    >
                      <Link href="/shop">
                        Alışverişe Devam Et
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
                
                {/* Security Info */}
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <CreditCard className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-900 mb-1">
                          Güvenli Ödeme
                        </h4>
                        <p className="text-sm text-blue-700">
                          256-bit SSL şifreleme ile korumalı ödeme sistemi
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
