'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { 
  HelpCircle, 
  Search, 
  ShoppingCart, 
  CreditCard, 
  Truck, 
  RotateCcw,
  User,
  Shield
} from 'lucide-react'

const faqCategories = [
  {
    id: 'general',
    title: 'Genel Sorular',
    icon: HelpCircle,
    questions: [
      {
        question: 'E-Marketing nedir?',
        answer: 'E-Marketing, geniş ürün yelpazesi ile kaliteli alışveriş deneyimi sunan online e-ticaret platformudur. Elektronikten giyime, ev eşyasından spora kadar birçok kategoride ürün bulabilirsiniz.'
      },
      {
        question: 'Üyelik zorunlu mu?',
        answer: 'Ürünleri incelemek için üyelik gerekmez, ancak sipariş verebilmek için üye olmanız gerekir. Üyelik tamamen ücretsizdir ve birçok avantaj sunar.'
      },
      {
        question: 'Müşteri hizmetlerine nasıl ulaşabilirim?',
        answer: 'Müşteri hizmetlerimize (0212) 123 45 67 numaralı telefondan, <EMAIL> e-posta adresinden veya WhatsApp (0532) 123 45 67 üzerinden ulaşabilirsiniz.'
      },
      {
        question: 'Çalışma saatleriniz nedir?',
        answer: 'Müşteri hizmetlerimiz Pazartesi-Cuma 09:00-18:00, Cumartesi 10:00-16:00 saatleri arasında hizmet vermektedir. Pazar günleri kapalıyız.'
      }
    ]
  },
  {
    id: 'account',
    title: 'Hesap ve Üyelik',
    icon: User,
    questions: [
      {
        question: 'Nasıl üye olabilirim?',
        answer: 'Ana sayfadaki "Üye Ol" butonuna tıklayarak ad, soyad, e-posta ve şifre bilgilerinizi girerek kolayca üye olabilirsiniz. E-posta doğrulaması sonrası hesabınız aktif olur.'
      },
      {
        question: 'Şifremi unuttum, ne yapmalıyım?',
        answer: 'Giriş sayfasındaki "Şifremi Unuttum" linkine tıklayın. E-posta adresinizi girin, size gönderilen link ile yeni şifre oluşturabilirsiniz.'
      },
      {
        question: 'Hesap bilgilerimi nasıl güncellerim?',
        answer: 'Giriş yaptıktan sonra "Hesabım" bölümünden kişisel bilgilerinizi, adres bilgilerinizi ve şifrenizi güncelleyebilirsiniz.'
      },
      {
        question: 'Hesabımı nasıl silebilirim?',
        answer: 'Hesap silme işlemi için müşteri hizmetlerimizle iletişime geçmeniz gerekmektedir. KVKK kapsamında verileriniz güvenli şekilde silinir.'
      }
    ]
  },
  {
    id: 'shopping',
    title: 'Alışveriş',
    icon: ShoppingCart,
    questions: [
      {
        question: 'Nasıl sipariş verebilirim?',
        answer: 'İstediğiniz ürünü seçin, sepete ekleyin, sepeti kontrol edin ve "Ödemeye Geç" butonuna tıklayın. Adres ve ödeme bilgilerinizi girerek siparişinizi tamamlayın.'
      },
      {
        question: 'Minimum sipariş tutarı var mı?',
        answer: 'Minimum sipariş tutarımız bulunmamaktadır. Ancak 150 TL üzeri siparişlerde kargo ücretsizdir.'
      },
      {
        question: 'Siparişimi nasıl iptal edebilirim?',
        answer: 'Henüz kargoya verilmemiş siparişleri "Siparişlerim" sayfasından iptal edebilirsiniz. Kargoya verilen siparişler için iade süreci başlatabilirsiniz.'
      },
      {
        question: 'Stokta olmayan ürünü sipariş edebilir miyim?',
        answer: 'Stokta olmayan ürünler sipariş edilemez. Ürün sayfasında "Stokta gelince haber ver" seçeneği ile bilgilendirilme talebinde bulunabilirsiniz.'
      },
      {
        question: 'Toplu sipariş verebilir miyim?',
        answer: 'Evet, toplu siparişler için özel fiyat ve koşullar sunuyoruz. Kurumsal satış ekibimizle iletişime geçin: <EMAIL>'
      }
    ]
  },
  {
    id: 'payment',
    title: 'Ödeme',
    icon: CreditCard,
    questions: [
      {
        question: 'Hangi ödeme yöntemlerini kabul ediyorsunuz?',
        answer: 'Kredi kartı, banka kartı, havale/EFT ve kapıda ödeme (nakit/kart) seçeneklerini kabul ediyoruz. Tüm kartlar için 3D Secure güvenlik sistemi kullanılır.'
      },
      {
        question: 'Ödeme güvenli mi?',
        answer: 'Evet, tüm ödemeler 256-bit SSL şifreleme ile korunur. Kart bilgileriniz saklanmaz ve PCI DSS standartlarına uygun güvenlik önlemleri alınır.'
      },
      {
        question: 'Taksit seçenekleri var mı?',
        answer: 'Kredi kartları için 2-12 aya varan taksit seçenekleri mevcuttur. Taksit oranları banka ve kart türüne göre değişir.'
      },
      {
        question: 'Kapıda ödeme nasıl çalışır?',
        answer: 'Kapıda ödeme seçeneğinde ürününüz adresinize geldiğinde nakit veya kredi kartı ile ödeme yapabilirsiniz. Bu seçenek için ek ücret alınmaz.'
      },
      {
        question: 'Fatura nasıl alırım?',
        answer: 'E-fatura otomatik olarak e-posta adresinize gönderilir. Kurumsal fatura için sipariş sırasında firma bilgilerinizi girmeniz yeterlidir.'
      }
    ]
  },
  {
    id: 'shipping',
    title: 'Kargo ve Teslimat',
    icon: Truck,
    questions: [
      {
        question: 'Kargo ücreti ne kadar?',
        answer: '150 TL üzeri siparişlerde kargo ücretsizdir. 150 TL altı siparişlerde standart kargo ücreti 29,90 TL, hızlı kargo 49,90 TL\'dir.'
      },
      {
        question: 'Ne kadar sürede teslim alırım?',
        answer: 'İstanbul içi 1-2 iş günü, büyük şehirler 2-3 iş günü, diğer iller 3-5 iş günü içinde teslim edilir. Hızlı kargo seçeneği ile süre kısaltılabilir.'
      },
      {
        question: 'Kargo takibi nasıl yapılır?',
        answer: 'Siparişiniz kargoya verildiğinde SMS ile takip numarası gönderilir. Bu numara ile kargo firmasının web sitesinden takip yapabilirsiniz.'
      },
      {
        question: 'Adresimde yokken teslimat yapılır mı?',
        answer: 'Adresinizde kimse yoksa kargo 3 kez deneme yapar. Sonrasında kargo şubesinden teslim alabilir veya yeni teslimat randevusu alabilirsiniz.'
      },
      {
        question: 'Farklı adrese teslimat yaptırabilir miyim?',
        answer: 'Evet, sipariş sırasında farklı teslimat adresi belirtebilirsiniz. Hediye gönderimlerinde alıcı bilgilerini eksiksiz girmeniz önemlidir.'
      }
    ]
  },
  {
    id: 'returns',
    title: 'İade ve Değişim',
    icon: RotateCcw,
    questions: [
      {
        question: 'İade koşulları nelerdir?',
        answer: 'Ürünleri teslim aldığınız tarihten itibaren 14 gün içinde, orijinal ambalajında, kullanılmamış ve hasarsız olarak iade edebilirsiniz.'
      },
      {
        question: 'Hangi ürünler iade edilemez?',
        answer: 'Hijyen gerektiren ürünler (iç giyim, kozmetik), kişiye özel üretilen ürünler, bozulabilir gıdalar ve mührü açılmış dijital içerikler iade edilemez.'
      },
      {
        question: 'İade masrafı kim karşılar?',
        answer: 'Hatalı ürün gönderiminde iade masrafı tarafımızca karşılanır. Müşteri kaynaklı iadelerde (beğenmeme, fikir değiştirme) kargo ücreti müşteriye aittir.'
      },
      {
        question: 'Para iadesi ne kadar sürer?',
        answer: 'İade edilen ürün kontrolü sonrası 5-10 iş günü içinde ödeme yaptığınız kartınıza para iadesi yapılır. Banka işlem süreleri değişkenlik gösterebilir.'
      },
      {
        question: 'Ürün değişimi yapabilir miyim?',
        answer: 'Evet, aynı ürünün farklı beden, renk veya modeliyle değişim yapabilirsiniz. Fiyat farkı varsa ek ödeme talep edilir veya fark iade edilir.'
      }
    ]
  },
  {
    id: 'security',
    title: 'Güvenlik ve Gizlilik',
    icon: Shield,
    questions: [
      {
        question: 'Kişisel bilgilerim güvende mi?',
        answer: 'Evet, tüm kişisel bilgileriniz KVKK kapsamında korunur. 256-bit SSL şifreleme ve güvenlik duvarları ile verileriniz güvende tutulur.'
      },
      {
        question: 'Kredi kartı bilgilerim saklanır mı?',
        answer: 'Hayır, kredi kartı bilgileriniz sistemimizde saklanmaz. Her işlemde yeniden girmeniz gerekir. Bu güvenliğiniz için önemlidir.'
      },
      {
        question: 'Hesabımın güvenliğini nasıl artırabilirim?',
        answer: 'Güçlü şifre kullanın, şifrenizi kimseyle paylaşmayın, düzenli olarak değiştirin ve şüpheli aktivite durumunda hemen bize bildirin.'
      },
      {
        question: 'Dolandırıcılık girişimlerinden nasıl korunurum?',
        answer: 'Asla şifrenizi paylaşmayın, şüpheli e-postalara tıklamayın, sadece resmi web sitemizden alışveriş yapın ve güvenilir ağlarda işlem yapın.'
      }
    ]
  }
]

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  
  const filteredQuestions = faqCategories.flatMap(category => 
    category.questions
      .filter(q => 
        (selectedCategory === 'all' || category.id === selectedCategory) &&
        (q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
         q.answer.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      .map(q => ({ ...q, categoryId: category.id, categoryTitle: category.title }))
  )
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <HelpCircle className="h-12 w-12 mr-4" />
            <h1 className="text-4xl font-bold">Sıkça Sorulan Sorular</h1>
          </div>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Aradığınız cevapları bulamıyor musunuz? En sık sorulan soruları derledik.
          </p>
          
          {/* Search */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Soru ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white text-gray-900"
              />
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          
          {/* Category Filter */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                onClick={() => setSelectedCategory('all')}
                size="sm"
              >
                Tümü
              </Button>
              {faqCategories.map((category) => {
                const IconComponent = category.icon
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory(category.id)}
                    size="sm"
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {category.title}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* FAQ Content */}
          {selectedCategory === 'all' ? (
            // Show by categories
            <div className="space-y-8">
              {faqCategories.map((category) => {
                const IconComponent = category.icon
                const categoryQuestions = category.questions.filter(q =>
                  q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  q.answer.toLowerCase().includes(searchQuery.toLowerCase())
                )
                
                if (categoryQuestions.length === 0 && searchQuery) return null
                
                return (
                  <Card key={category.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center mb-4">
                        <IconComponent className="h-6 w-6 text-primary mr-3" />
                        <h2 className="text-2xl font-bold">{category.title}</h2>
                      </div>
                      
                      <Accordion type="single" collapsible className="w-full">
                        {categoryQuestions.map((faq, index) => (
                          <AccordionItem key={index} value={`${category.id}-${index}`}>
                            <AccordionTrigger className="text-left">
                              {faq.question}
                            </AccordionTrigger>
                            <AccordionContent className="text-gray-700">
                              {faq.answer}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          ) : (
            // Show filtered results
            <Card>
              <CardContent className="p-6">
                <h2 className="text-2xl font-bold mb-4">
                  {searchQuery ? 'Arama Sonuçları' : 
                   faqCategories.find(c => c.id === selectedCategory)?.title}
                </h2>
                
                {filteredQuestions.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredQuestions.map((faq, index) => (
                      <AccordionItem key={index} value={`filtered-${index}`}>
                        <AccordionTrigger className="text-left">
                          {faq.question}
                          {searchQuery && (
                            <span className="text-sm text-gray-500 ml-2">
                              ({faq.categoryTitle})
                            </span>
                          )}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-700">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <div className="text-center py-8">
                    <HelpCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Sonuç bulunamadı
                    </h3>
                    <p className="text-gray-600">
                      Aradığınız kriterlere uygun soru bulunamadı.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Contact CTA */}
          <Card className="mt-12 bg-blue-50 border-blue-200">
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-bold text-blue-900 mb-4">
                Aradığınız cevabı bulamadınız mı?
              </h3>
              <p className="text-blue-700 mb-6">
                Müşteri hizmetlerimiz size yardımcı olmaya hazır. 
                7/24 destek alabilirsiniz.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <a href="tel:+902121234567">
                    📞 (0212) 123 45 67
                  </a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="mailto:<EMAIL>">
                    ✉️ E-posta Gönder
                  </a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/contact">
                    💬 İletişim Formu
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
