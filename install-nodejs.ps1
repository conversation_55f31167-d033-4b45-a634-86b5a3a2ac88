# E-Marketing Node.js <PERSON><PERSON><PERSON>ript'i
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    E-Marketing Node.js Kurulumu" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Node.js'in yüklü olup olmadığını kontrol et
Write-Host "Node.js kontrol ediliyor..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    $npmVersion = npm --version 2>$null
    
    if ($nodeVersion -and $npmVersion) {
        Write-Host "✅ Node.js zaten yüklü!" -ForegroundColor Green
        Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
        Write-Host "npm: $npmVersion" -ForegroundColor Green
        Write-Host ""
        
        # Proje klasörüne git ve server'ı başlat
        Write-Host "Proje klasörüne gidiliyor..." -ForegroundColor Yellow
        Set-Location "C:\Users\<USER>\Desktop\e-marketing"
        
        Write-Host "Dependencies kontrol ediliyor..." -ForegroundColor Yellow
        if (!(Test-Path "node_modules")) {
            Write-Host "Dependencies yükleniyor..." -ForegroundColor Yellow
            npm install
        }
        
        Write-Host ""
        Write-Host "🚀 Development server başlatılıyor..." -ForegroundColor Green
        Write-Host "Ana site: http://localhost:3000" -ForegroundColor Cyan
        Write-Host "Server'ı durdurmak için Ctrl+C basın" -ForegroundColor Yellow
        Write-Host ""
        
        npm run dev
        exit
    }
} catch {
    Write-Host "❌ Node.js yüklü değil." -ForegroundColor Red
}

# Winget ile kurulum dene
Write-Host "Winget ile Node.js yükleniyor..." -ForegroundColor Yellow
try {
    winget install OpenJS.NodeJS --accept-source-agreements --accept-package-agreements
    Write-Host "✅ Node.js başarıyla yüklendi!" -ForegroundColor Green
} catch {
    Write-Host "❌ Winget kurulumu başarısız." -ForegroundColor Red
    
    # Chocolatey ile kurulum dene
    Write-Host "Chocolatey kontrol ediliyor..." -ForegroundColor Yellow
    try {
        choco --version 2>$null
        Write-Host "Chocolatey ile Node.js yükleniyor..." -ForegroundColor Yellow
        choco install nodejs -y
        Write-Host "✅ Node.js başarıyla yüklendi!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Chocolatey bulunamadı." -ForegroundColor Red
        Write-Host ""
        Write-Host "🔗 Manuel kurulum gerekli:" -ForegroundColor Yellow
        Write-Host "1. https://nodejs.org/ adresine gidin" -ForegroundColor White
        Write-Host "2. LTS versiyonunu indirin" -ForegroundColor White
        Write-Host "3. .msi dosyasını çalıştırın" -ForegroundColor White
        Write-Host "4. Kurulum tamamlandıktan sonra yeni terminal açın" -ForegroundColor White
        Write-Host "5. Bu script'i tekrar çalıştırın" -ForegroundColor White
        Write-Host ""
        
        # Browser'da Node.js sayfasını aç
        Start-Process "https://nodejs.org/"
        
        Read-Host "Manuel kurulum tamamlandıktan sonra Enter'a basın"
    }
}

# Kurulum sonrası test
Write-Host ""
Write-Host "Kurulum test ediliyor..." -ForegroundColor Yellow
Write-Host "Yeni PowerShell penceresi açılıyor..." -ForegroundColor Yellow

# Yeni PowerShell penceresi aç ve test et
$testScript = @"
Write-Host 'Node.js test ediliyor...' -ForegroundColor Yellow
try {
    `$nodeVersion = node --version
    `$npmVersion = npm --version
    Write-Host '✅ Node.js başarıyla yüklendi!' -ForegroundColor Green
    Write-Host 'Node.js: ' `$nodeVersion -ForegroundColor Green
    Write-Host 'npm: ' `$npmVersion -ForegroundColor Green
    
    Write-Host ''
    Write-Host 'Proje klasörüne gidiliyor...' -ForegroundColor Yellow
    Set-Location 'C:\Users\<USER>\Desktop\e-marketing'
    
    Write-Host 'Dependencies yükleniyor...' -ForegroundColor Yellow
    npm install
    
    Write-Host ''
    Write-Host '🚀 Development server başlatılıyor...' -ForegroundColor Green
    Write-Host 'Ana site: http://localhost:3000' -ForegroundColor Cyan
    Write-Host 'Server''ı durdurmak için Ctrl+C basın' -ForegroundColor Yellow
    Write-Host ''
    
    npm run dev
} catch {
    Write-Host '❌ Hala sorun var. Manuel kurulum gerekli.' -ForegroundColor Red
    Write-Host 'https://nodejs.org/ adresinden manuel olarak yükleyin.' -ForegroundColor Yellow
}
Read-Host 'Devam etmek için Enter basın'
"@

Start-Process powershell -ArgumentList "-NoExit", "-Command", $testScript
