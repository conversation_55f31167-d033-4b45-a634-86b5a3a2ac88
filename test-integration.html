<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Marketing Entegrasyon Testi</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="./lib/data-bridge.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8">E-Marketing Entegrasyon Testi</h1>
        
        <!-- Test Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <button onclick="testDataBridge()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                DataBridge Test
            </button>
            <button onclick="testProductAdd()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                <PERSON><PERSON><PERSON><PERSON> Test
            </button>
            <button onclick="testRealTimeSync()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                Gerçek Zamanlı Senkronizasyon Test
            </button>
        </div>
        
        <!-- Test Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- DataBridge Status -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">DataBridge Durumu</h2>
                <div id="databridge-status" class="space-y-2"></div>
            </div>
            
            <!-- Products -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Ürünler</h2>
                <div id="products-list" class="space-y-2"></div>
            </div>
            
            <!-- Categories -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Kategoriler</h2>
                <div id="categories-list" class="space-y-2"></div>
            </div>
            
            <!-- Logs -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Test Logları</h2>
                <div id="test-logs" class="bg-gray-100 p-4 rounded text-sm font-mono h-64 overflow-y-auto"></div>
            </div>
        </div>
        
        <!-- Links -->
        <div class="mt-8 flex space-x-4">
            <a href="admin-panel.html" target="_blank" class="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-900">
                Admin Panel Aç
            </a>
            <a href="preview.html" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Preview Aç
            </a>
            <a href="http://localhost:3000" target="_blank" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                Ana Site Aç
            </a>
        </div>
    </div>

    <script>
        let testLogs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLogs.push(logEntry);
            
            const logsContainer = document.getElementById('test-logs');
            logsContainer.innerHTML = testLogs.join('\n');
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            console.log(logEntry);
        }
        
        function testDataBridge() {
            log('DataBridge testi başlatılıyor...');
            
            try {
                if (!window.dataBridge) {
                    log('DataBridge bulunamadı!', 'error');
                    return;
                }
                
                log('DataBridge bulundu ✓', 'success');
                
                const data = window.dataBridge.getData();
                log(`Veri yapısı: ${data ? 'Mevcut' : 'Yok'}`, data ? 'success' : 'error');
                
                const products = window.dataBridge.getProducts();
                log(`Ürün sayısı: ${products.length}`, 'info');
                
                const categories = window.dataBridge.getData()?.categories || [];
                log(`Kategori sayısı: ${categories.length}`, 'info');
                
                updateStatus();
                
            } catch (error) {
                log(`DataBridge test hatası: ${error.message}`, 'error');
            }
        }
        
        function testProductAdd() {
            log('Ürün ekleme testi başlatılıyor...');
            
            try {
                const testProduct = {
                    name: `Test Ürün ${Date.now()}`,
                    description: 'Bu bir test ürünüdür',
                    price: Math.floor(Math.random() * 1000) + 100,
                    categoryId: 'electronics',
                    isActive: true,
                    isFeatured: true,
                    images: [{
                        url: 'https://via.placeholder.com/300x300/3b82f6/ffffff?text=Test',
                        alt: 'Test Ürün'
                    }]
                };
                
                const success = window.dataBridge.addProduct(testProduct);
                
                if (success) {
                    log('Test ürün başarıyla eklendi ✓', 'success');
                    updateStatus();
                } else {
                    log('Test ürün eklenemedi!', 'error');
                }
                
            } catch (error) {
                log(`Ürün ekleme hatası: ${error.message}`, 'error');
            }
        }
        
        function testRealTimeSync() {
            log('Gerçek zamanlı senkronizasyon testi başlatılıyor...');
            
            // Listen for data changes
            window.addEventListener('databridge:change', (event) => {
                log('DataBridge değişiklik eventi alındı ✓', 'success');
                updateStatus();
            });
            
            // Listen for storage changes
            window.addEventListener('storage', (event) => {
                if (event.key === 'e-marketing-data') {
                    log('Storage değişiklik eventi alındı ✓', 'success');
                }
            });
            
            log('Event listener\'lar kuruldu. Admin panelden değişiklik yapın.', 'info');
        }
        
        function updateStatus() {
            // Update DataBridge status
            const statusContainer = document.getElementById('databridge-status');
            const isLoaded = !!window.dataBridge;
            const data = window.dataBridge?.getData();
            
            statusContainer.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full ${isLoaded ? 'bg-green-500' : 'bg-red-500'}"></div>
                    <span>DataBridge: ${isLoaded ? 'Aktif' : 'İnaktif'}</span>
                </div>
                <div class="text-sm text-gray-600">
                    Veri: ${data ? 'Yüklü' : 'Yok'}
                </div>
            `;
            
            // Update products list
            const productsContainer = document.getElementById('products-list');
            const products = window.dataBridge?.getProducts() || [];
            
            productsContainer.innerHTML = products.length > 0 
                ? products.slice(0, 5).map(p => `
                    <div class="flex justify-between items-center py-1 border-b">
                        <span class="text-sm">${p.name}</span>
                        <span class="text-xs text-gray-500">₺${p.price}</span>
                    </div>
                `).join('') + (products.length > 5 ? `<div class="text-xs text-gray-500 mt-2">+${products.length - 5} daha...</div>` : '')
                : '<div class="text-gray-500 text-sm">Ürün bulunamadı</div>';
            
            // Update categories list
            const categoriesContainer = document.getElementById('categories-list');
            const categories = window.dataBridge?.getData()?.categories || [];
            
            categoriesContainer.innerHTML = categories.length > 0
                ? categories.map(c => `
                    <div class="py-1 border-b">
                        <span class="text-sm">${c.name}</span>
                    </div>
                `).join('')
                : '<div class="text-gray-500 text-sm">Kategori bulunamadı</div>';
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Test sayfası yüklendi');
            
            // Wait for DataBridge to initialize
            setTimeout(() => {
                testDataBridge();
            }, 500);
        });
    </script>
</body>
</html>
