import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Truck, Clock, MapPin, Package, CreditCard, AlertTriangle } from 'lucide-react'

export default function ShippingPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <Truck className="h-12 w-12 mr-4" />
            <h1 className="text-4xl font-bold">Kargo ve Teslimat</h1>
          </div>
          <p className="text-xl max-w-3xl mx-auto">
            Siparişlerinizi güvenli ve hızlı şekilde adresinize ulaştırıyoruz. 
            Detaylı kargo bilgileri ve teslimat koşulları.
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* Quick Info */}
          <div className="grid md:grid-cols-4 gap-4">
            <Card className="text-center">
              <CardContent className="p-4">
                <Package className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-sm">Ücretsiz Kargo</h3>
                <p className="text-xs text-gray-600">150 TL üzeri</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-4">
                <Clock className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-sm">Hızlı Teslimat</h3>
                <p className="text-xs text-gray-600">1-5 iş günü</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-4">
                <MapPin className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-sm">Türkiye Geneli</h3>
                <p className="text-xs text-gray-600">Tüm iller</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-4">
                <Truck className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-sm">Güvenli Kargo</h3>
                <p className="text-xs text-gray-600">Sigortalı</p>
              </CardContent>
            </Card>
          </div>

          {/* Shipping Costs */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <CreditCard className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Kargo Ücretleri</h2>
              </div>
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <Package className="h-8 w-8 text-green-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-green-800">Ücretsiz Kargo</h3>
                        <Badge className="bg-green-100 text-green-800 mt-1">150 TL ve üzeri</Badge>
                      </div>
                    </div>
                    <ul className="list-disc list-inside text-green-700 space-y-1">
                      <li>Standart teslimat süresi</li>
                      <li>Güvenli ambalajlama</li>
                      <li>Takip numarası</li>
                      <li>SMS bilgilendirme</li>
                    </ul>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <Truck className="h-8 w-8 text-blue-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-semibold text-blue-800">Standart Kargo</h3>
                        <Badge className="bg-blue-100 text-blue-800 mt-1">29,90 TL</Badge>
                      </div>
                    </div>
                    <ul className="list-disc list-inside text-blue-700 space-y-1">
                      <li>150 TL altı siparişler</li>
                      <li>2-5 iş günü teslimat</li>
                      <li>Kapıda ödeme seçeneği</li>
                      <li>Güvenli paketleme</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <Clock className="h-8 w-8 text-purple-600 mr-3" />
                    <div>
                      <h3 className="text-lg font-semibold text-purple-800">Hızlı Kargo</h3>
                      <Badge className="bg-purple-100 text-purple-800 mt-1">49,90 TL</Badge>
                    </div>
                  </div>
                  <div className="grid md:grid-cols-2 gap-4">
                    <ul className="list-disc list-inside text-purple-700 space-y-1">
                      <li>Aynı gün kargo (İstanbul)</li>
                      <li>1-2 iş günü (diğer iller)</li>
                      <li>Öncelikli işlem</li>
                      <li>SMS + E-posta bilgilendirme</li>
                    </ul>
                    <div className="text-sm text-purple-600">
                      <p><strong>Hızlı kargo saatleri:</strong></p>
                      <p>Hafta içi: 14:00'a kadar</p>
                      <p>Cumartesi: 12:00'a kadar</p>
                      <p>Pazar: Hizmet yok</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Times */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Clock className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Teslimat Süreleri</h2>
              </div>
              <div className="space-y-6">
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">İstanbul</h3>
                    <p className="text-gray-600 mb-2">1-2 iş günü</p>
                    <Badge variant="secondary">Aynı gün seçeneği</Badge>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Büyük Şehirler</h3>
                    <p className="text-gray-600 mb-2">2-3 iş günü</p>
                    <Badge variant="secondary">Ankara, İzmir, Bursa</Badge>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MapPin className="h-8 w-8 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-semibold mb-2">Diğer İller</h3>
                    <p className="text-gray-600 mb-2">3-5 iş günü</p>
                    <Badge variant="secondary">Türkiye geneli</Badge>
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-yellow-900 mb-1">Önemli Bilgiler</h4>
                      <ul className="text-yellow-700 text-sm space-y-1">
                        <li>• Teslimat süreleri sipariş onayından sonra başlar</li>
                        <li>• Hafta sonları ve resmi tatillerde teslimat yapılmaz</li>
                        <li>• Hava koşulları teslimat süresini etkileyebilir</li>
                        <li>• Özel günlerde (bayram, yılbaşı) süre uzayabilir</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cargo Companies */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Truck className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Kargo Firmaları</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">
                  Güvenilir kargo firmaları ile çalışarak siparişlerinizin güvenli şekilde 
                  ulaşmasını sağlıyoruz.
                </p>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Anlaşmalı Kargo Firmaları</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Yurtiçi Kargo</li>
                      <li>MNG Kargo</li>
                      <li>Aras Kargo</li>
                      <li>PTT Kargo</li>
                      <li>UPS</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Kargo Özellikleri</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Sigortalı gönderim</li>
                      <li>Takip numarası</li>
                      <li>SMS bilgilendirme</li>
                      <li>Güvenli ambalajlama</li>
                      <li>İmzalı teslim</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Special Products */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Package className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Özel Ürün Teslimatı</h2>
              </div>
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-2">Büyük/Ağır Ürünler</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Beyaz eşya, mobilya</li>
                      <li>Özel kargo ücreti</li>
                      <li>Randevulu teslimat</li>
                      <li>Montaj hizmeti (opsiyonel)</li>
                    </ul>
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-2">Kırılabilir Ürünler</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Cam, seramik ürünler</li>
                      <li>Özel ambalajlama</li>
                      <li>Ekstra sigorta</li>
                      <li>Dikkatli taşıma</li>
                    </ul>
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-2">Elektronik Ürünler</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Antistatik ambalaj</li>
                      <li>Orijinal kutu ile gönderim</li>
                      <li>Ek koruma malzemesi</li>
                      <li>Hızlı teslimat önceliği</li>
                    </ul>
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h3 className="font-semibold mb-2">Gıda Ürünleri</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Soğuk zincir korunumu</li>
                      <li>Hızlı teslimat</li>
                      <li>Özel ambalajlama</li>
                      <li>Son kullanma tarihi kontrolü</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Process */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Clock className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Teslimat Süreci</h2>
              </div>
              <div className="space-y-6">
                <div className="grid gap-4">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      1
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Sipariş Onayı</h3>
                      <p className="text-gray-700 text-sm">
                        Siparişiniz alındıktan sonra stok kontrolü yapılır ve onaylanır.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      2
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Hazırlık</h3>
                      <p className="text-gray-700 text-sm">
                        Ürünleriniz özenle paketlenir ve kargo için hazırlanır.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      3
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Kargo Çıkışı</h3>
                      <p className="text-gray-700 text-sm">
                        Paketiniz kargo firmasına teslim edilir ve takip numarası gönderilir.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      4
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Teslimat</h3>
                      <p className="text-gray-700 text-sm">
                        Paketiniz belirtilen adrese güvenli şekilde teslim edilir.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 mb-2">Teslimat Bilgilendirme</h3>
                  <ul className="text-blue-700 text-sm space-y-1">
                    <li>• Kargo çıkışında SMS ile bilgilendirilirsiniz</li>
                    <li>• Teslimat günü tekrar SMS gönderilir</li>
                    <li>• Kargo takip numarası ile durumu izleyebilirsiniz</li>
                    <li>• Teslimat sırasında kimlik kontrolü yapılabilir</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact */}
          <Card>
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Kargo Desteği</h2>
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">
                  Kargo ve teslimatla ilgili sorularınız için müşteri hizmetlerimizle iletişime geçin.
                </p>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold mb-2">İletişim</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Telefon:</strong> (0212) 123 45 67</p>
                      <p><strong>E-posta:</strong> <EMAIL></p>
                      <p><strong>WhatsApp:</strong> (0532) 123 45 67</p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold mb-2">Çalışma Saatleri</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Hafta içi:</strong> 09:00 - 18:00</p>
                      <p><strong>Cumartesi:</strong> 10:00 - 16:00</p>
                      <p><strong>Pazar:</strong> Kapalı</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
