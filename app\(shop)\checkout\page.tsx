'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, CreditCard, Truck, Shield, Plus } from 'lucide-react'
import { useAuthStore } from '@/lib/store/auth-store'
import { useCartStore } from '@/lib/store/cart-store'
import { formatPrice } from '@/lib/utils'
import { Address } from '@/types'
import toast from 'react-hot-toast'

const checkoutSchema = z.object({
  addressId: z.string().min(1, 'Teslimat adresi seçiniz'),
  paymentMethod: z.string().min(1, 'Ödeme yöntemi seçiniz'),
  notes: z.string().optional(),
})

type CheckoutForm = z.infer<typeof checkoutSchema>

export default function CheckoutPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const { items, totalPrice, fetchCart } = useCartStore()
  
  const [addresses, setAddresses] = useState<Address[]>([])
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CheckoutForm>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      paymentMethod: 'stripe',
    }
  })
  
  const selectedAddressId = watch('addressId')
  const selectedPaymentMethod = watch('paymentMethod')
  
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/checkout')
      return
    }
    
    fetchCart()
    fetchAddresses()
  }, [isAuthenticated, router, fetchCart])
  
  useEffect(() => {
    if (items.length === 0) {
      router.push('/cart')
    }
  }, [items, router])
  
  const fetchAddresses = async () => {
    try {
      const response = await fetch('/api/addresses')
      const data = await response.json()
      
      if (data.success) {
        setAddresses(data.data)
        
        // Auto-select default address
        const defaultAddress = data.data.find((addr: Address) => addr.isDefault)
        if (defaultAddress) {
          setValue('addressId', defaultAddress.id)
        }
      }
    } catch (error) {
      console.error('Error fetching addresses:', error)
    } finally {
      setIsLoadingAddresses(false)
    }
  }
  
  const onSubmit = async (data: CheckoutForm) => {
    setIsProcessing(true)
    
    try {
      // Prepare order items
      const orderItems = items.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.product.price,
      }))
      
      // Create checkout session
      const response = await fetch('/api/checkout/create-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: orderItems,
          addressId: data.addressId,
          notes: data.notes,
        }),
      })
      
      const result = await response.json()
      
      if (result.success) {
        // Redirect to Stripe Checkout
        window.location.href = result.data.url
      } else {
        toast.error(result.error || 'Ödeme işlemi başlatılırken hata oluştu')
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error('Ödeme işlemi başlatılırken hata oluştu')
    } finally {
      setIsProcessing(false)
    }
  }
  
  const shippingCost = totalPrice >= 150 ? 0 : 29.99
  const taxAmount = totalPrice * 0.18 // 18% KDV
  const finalTotal = totalPrice + shippingCost + taxAmount
  
  const selectedAddress = addresses.find(addr => addr.id === selectedAddressId)
  
  if (!isAuthenticated) {
    return null
  }
  
  if (items.length === 0) {
    return null
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" asChild>
                <Link href="/cart">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Ödeme</h1>
                <p className="text-gray-600">Siparişinizi tamamlayın</p>
              </div>
            </div>
            
            {/* Progress Steps */}
            <div className="hidden md:flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <span className="ml-2 text-sm font-medium text-primary">Sepet</span>
              </div>
              <div className="w-8 h-px bg-gray-300" />
              <div className="flex items-center">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <span className="ml-2 text-sm font-medium text-primary">Ödeme</span>
              </div>
              <div className="w-8 h-px bg-gray-300" />
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <span className="ml-2 text-sm text-gray-600">Tamamlandı</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Checkout Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Delivery Address */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Teslimat Adresi</span>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/profile/addresses">
                        <Plus className="h-4 w-4 mr-2" />
                        Yeni Adres
                      </Link>
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isLoadingAddresses ? (
                    <div className="space-y-3">
                      {[...Array(2)].map((_, i) => (
                        <div key={i} className="h-20 bg-gray-200 rounded-md animate-pulse" />
                      ))}
                    </div>
                  ) : addresses.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-600 mb-4">Henüz kayıtlı adresiniz yok</p>
                      <Button asChild>
                        <Link href="/profile/addresses">Adres Ekle</Link>
                      </Button>
                    </div>
                  ) : (
                    <RadioGroup
                      value={selectedAddressId}
                      onValueChange={(value) => setValue('addressId', value)}
                    >
                      {addresses.map((address) => (
                        <div key={address.id} className="flex items-start space-x-3">
                          <RadioGroupItem value={address.id} id={address.id} className="mt-1" />
                          <Label htmlFor={address.id} className="flex-1 cursor-pointer">
                            <div className="border rounded-lg p-4 hover:border-primary transition-colors">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium">{address.title}</h4>
                                {address.isDefault && (
                                  <span className="text-xs bg-primary text-white px-2 py-1 rounded">
                                    Varsayılan
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">
                                {address.firstName} {address.lastName}
                              </p>
                              <p className="text-sm text-gray-600">
                                {address.address}
                              </p>
                              <p className="text-sm text-gray-600">
                                {address.district}, {address.city} {address.postalCode}
                              </p>
                              <p className="text-sm text-gray-600">
                                {address.phone}
                              </p>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  )}
                  {errors.addressId && (
                    <p className="text-sm text-red-600">{errors.addressId.message}</p>
                  )}
                </CardContent>
              </Card>
              
              {/* Payment Method */}
              <Card>
                <CardHeader>
                  <CardTitle>Ödeme Yöntemi</CardTitle>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={selectedPaymentMethod}
                    onValueChange={(value) => setValue('paymentMethod', value)}
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="stripe" id="stripe" />
                      <Label htmlFor="stripe" className="flex-1 cursor-pointer">
                        <div className="border rounded-lg p-4 hover:border-primary transition-colors">
                          <div className="flex items-center space-x-3">
                            <CreditCard className="h-6 w-6 text-primary" />
                            <div>
                              <h4 className="font-medium">Kredi/Banka Kartı</h4>
                              <p className="text-sm text-gray-600">
                                Visa, Mastercard, American Express
                              </p>
                            </div>
                          </div>
                        </div>
                      </Label>
                    </div>
                  </RadioGroup>
                  {errors.paymentMethod && (
                    <p className="text-sm text-red-600 mt-2">{errors.paymentMethod.message}</p>
                  )}
                </CardContent>
              </Card>
              
              {/* Order Notes */}
              <Card>
                <CardHeader>
                  <CardTitle>Sipariş Notları (Opsiyonel)</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Siparişiniz hakkında özel notlarınız..."
                    {...register('notes')}
                  />
                </CardContent>
              </Card>
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardHeader>
                  <CardTitle>Sipariş Özeti</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Order Items */}
                  <div className="space-y-3">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-3">
                        <div className="relative w-12 h-12 flex-shrink-0">
                          <Image
                            src={item.product.images?.[0]?.url || '/placeholder-product.jpg'}
                            alt={item.product.name}
                            fill
                            className="object-cover rounded-md"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {item.product.name}
                          </p>
                          <p className="text-sm text-gray-600">
                            {item.quantity} x {formatPrice(item.product.price)}
                          </p>
                        </div>
                        <span className="text-sm font-medium">
                          {formatPrice(item.quantity * item.product.price)}
                        </span>
                      </div>
                    ))}
                  </div>
                  
                  <Separator />
                  
                  {/* Totals */}
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Ara Toplam</span>
                      <span>{formatPrice(totalPrice)}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span>Kargo</span>
                      <span className={shippingCost === 0 ? 'text-green-600' : ''}>
                        {shippingCost === 0 ? 'Ücretsiz' : formatPrice(shippingCost)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span>KDV (%18)</span>
                      <span>{formatPrice(taxAmount)}</span>
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between text-lg font-bold">
                      <span>Toplam</span>
                      <span>{formatPrice(finalTotal)}</span>
                    </div>
                  </div>
                  
                  {/* Selected Address Summary */}
                  {selectedAddress && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-2">Teslimat Adresi</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>{selectedAddress.firstName} {selectedAddress.lastName}</p>
                          <p>{selectedAddress.address}</p>
                          <p>{selectedAddress.district}, {selectedAddress.city}</p>
                          <p>{selectedAddress.phone}</p>
                        </div>
                      </div>
                    </>
                  )}
                  
                  {/* Submit Button */}
                  <Button
                    type="submit"
                    className="w-full"
                    size="lg"
                    disabled={isProcessing || !selectedAddressId}
                  >
                    {isProcessing ? (
                      'İşleniyor...'
                    ) : (
                      <>
                        <CreditCard className="h-5 w-5 mr-2" />
                        Ödemeye Geç
                      </>
                    )}
                  </Button>
                  
                  {/* Security Info */}
                  <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                    <Shield className="h-4 w-4" />
                    <span>256-bit SSL ile güvenli ödeme</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
