// User Types
export interface User {
  id: string
  email: string
  name?: string
  phone?: string
  avatar?: string
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN'
  createdAt: Date
  updatedAt: Date
}

export interface Profile {
  id: string
  userId: string
  firstName?: string
  lastName?: string
  dateOfBirth?: Date
  gender?: 'MALE' | 'FEMALE' | 'OTHER'
  createdAt: Date
  updatedAt: Date
}

export interface Address {
  id: string
  userId: string
  title: string
  firstName: string
  lastName: string
  phone: string
  address: string
  district: string
  city: string
  postalCode: string
  isDefault: boolean
  addressType: 'HOME' | 'WORK' | 'OTHER'
  createdAt: Date
  updatedAt: Date
}

// Product Types
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  isActive: boolean
  sortOrder: number
  createdAt: Date
  updatedAt: Date
  children?: Category[]
  parent?: Category
}

export interface Product {
  id: string
  name: string
  slug: string
  description?: string
  price: number
  comparePrice?: number
  sku?: string
  stock: number
  isActive: boolean
  isFeatured: boolean
  weight?: number
  dimensions?: string
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  categoryId: string
  createdAt: Date
  updatedAt: Date
  category?: Category
  images?: ProductImage[]
  reviews?: Review[]
}

export interface ProductImage {
  id: string
  productId: string
  url: string
  alt?: string
  sortOrder: number
  createdAt: Date
}

export interface Tag {
  id: string
  name: string
  slug: string
  createdAt: Date
}

// Cart Types
export interface CartItem {
  id: string
  userId: string
  productId: string
  quantity: number
  createdAt: Date
  updatedAt: Date
  product?: Product
}

// Order Types
export interface Order {
  id: string
  userId: string
  orderNumber: string
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
  totalAmount: number
  shippingCost: number
  taxAmount: number
  discountAmount: number
  paymentMethod?: string
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'
  notes?: string
  addressId: string
  createdAt: Date
  updatedAt: Date
  address?: Address
  orderItems?: OrderItem[]
  shippingInfo?: ShippingInfo
}

export interface OrderItem {
  id: string
  orderId: string
  productId: string
  quantity: number
  price: number
  total: number
  product?: Product
}

export interface ShippingInfo {
  id: string
  orderId: string
  carrier: string
  trackingNumber?: string
  status: 'PREPARING' | 'SHIPPED' | 'IN_TRANSIT' | 'OUT_FOR_DELIVERY' | 'DELIVERED' | 'RETURNED'
  shippedAt?: Date
  deliveredAt?: Date
  estimatedDelivery?: Date
  createdAt: Date
  updatedAt: Date
}

// Review Types
export interface Review {
  id: string
  userId: string
  productId: string
  rating: number
  title?: string
  comment?: string
  isVerified: boolean
  createdAt: Date
  updatedAt: Date
  user?: User
}

export interface Favorite {
  id: string
  userId: string
  productId: string
  createdAt: Date
  product?: Product
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form Types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  name: string
  phone?: string
}

export interface AddressForm {
  title: string
  firstName: string
  lastName: string
  phone: string
  address: string
  district: string
  city: string
  postalCode: string
  addressType: 'HOME' | 'WORK' | 'OTHER'
  isDefault: boolean
}

export interface ProductForm {
  name: string
  description?: string
  price: number
  comparePrice?: number
  sku?: string
  stock: number
  categoryId: string
  isActive: boolean
  isFeatured: boolean
  weight?: number
  dimensions?: string
}

// Filter Types
export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
  featured?: boolean
  search?: string
  sortBy?: 'name' | 'price' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface OrderFilters {
  status?: string
  paymentStatus?: string
  dateFrom?: string
  dateTo?: string
  search?: string
  page?: number
  limit?: number
}
