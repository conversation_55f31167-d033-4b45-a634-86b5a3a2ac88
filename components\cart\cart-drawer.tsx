'use client'

import { useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Sheet, <PERSON>et<PERSON>ontent, SheetHeader, Sheet<PERSON><PERSON>le, SheetTrigger } from '@/components/ui/sheet'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { ShoppingCart, Plus, Minus, Trash2, X } from 'lucide-react'
import { useCartStore } from '@/lib/store/cart-store'
import { formatPrice } from '@/lib/utils'
import toast from 'react-hot-toast'

export function CartDrawer() {
  const {
    items,
    isOpen,
    totalItems,
    totalPrice,
    isUpdating,
    setOpen,
    updateQuantity,
    removeItem,
    fetchCart,
  } = useCartStore()
  
  useEffect(() => {
    fetchCart()
  }, [fetchCart])
  
  const handleUpdateQuantity = async (itemId: string, quantity: number) => {
    const result = await updateQuantity(itemId, quantity)
    if (!result.success) {
      toast.error(result.error || 'Miktar güncellenirken hata oluştu')
    }
  }
  
  const handleRemoveItem = async (itemId: string) => {
    const result = await removeItem(itemId)
    if (result.success) {
      toast.success('Ürün sepetten çıkarıldı')
    } else {
      toast.error(result.error || 'Ürün çıkarılırken hata oluştu')
    }
  }
  
  return (
    <Sheet open={isOpen} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <ShoppingCart className="h-5 w-5" />
          {totalItems > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {totalItems}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      
      <SheetContent className="flex flex-col w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-between">
            <span>Sepetim ({totalItems} ürün)</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </SheetTitle>
        </SheetHeader>
        
        <div className="flex-1 overflow-y-auto">
          {items.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <ShoppingCart className="h-16 w-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Sepetiniz boş
              </h3>
              <p className="text-gray-500 mb-6">
                Alışverişe başlamak için ürün ekleyin
              </p>
              <Button onClick={() => setOpen(false)} asChild>
                <Link href="/shop">Alışverişe Başla</Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {items.map((item) => (
                <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                  {/* Product Image */}
                  <div className="relative w-16 h-16 flex-shrink-0">
                    <Image
                      src={item.product.images?.[0]?.url || '/placeholder-product.jpg'}
                      alt={item.product.name}
                      fill
                      className="object-cover rounded-md"
                    />
                  </div>
                  
                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {item.product.name}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {item.product.category?.name}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-sm font-medium text-gray-900">
                        {formatPrice(item.product.price)}
                      </span>
                      
                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                          disabled={isUpdating || item.quantity <= 1}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        
                        <span className="text-sm font-medium min-w-[2rem] text-center">
                          {item.quantity}
                        </span>
                        
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                          disabled={isUpdating || item.quantity >= item.product.stock}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Remove Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-red-500 hover:text-red-700"
                    onClick={() => handleRemoveItem(item.id)}
                    disabled={isUpdating}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Cart Footer */}
        {items.length > 0 && (
          <div className="border-t pt-4 space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-base font-medium text-gray-900">
                Toplam
              </span>
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(totalPrice)}
              </span>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Button
                className="w-full"
                onClick={() => setOpen(false)}
                asChild
              >
                <Link href="/cart">Sepeti Görüntüle</Link>
              </Button>
              
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setOpen(false)}
                asChild
              >
                <Link href="/checkout">Hızlı Satın Al</Link>
              </Button>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-gray-500">
                150₺ üzeri siparişlerde ücretsiz kargo
              </p>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}
