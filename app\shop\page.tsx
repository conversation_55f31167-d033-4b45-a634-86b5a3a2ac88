'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { ProductCard } from '@/components/products/product-card'
import { LoadingCard } from '@/components/ui/loading-spinner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List,
  SlidersHorizontal,
  Package
} from 'lucide-react'
import { useProductStore } from '@/lib/store/product-store'
import { useDataBridgeContext } from '@/components/providers/data-bridge-provider'
import { Product, Category } from '@/types'

export default function ShopPage() {
  const searchParams = useSearchParams()
  const {
    products: storeProducts,
    categories: storeCategories,
    isLoading,
    pagination,
    searchQuery,
    filters,
    fetchProducts,
    fetchCategories,
    setSearchQuery,
    setFilters,
    clearFilters
  } = useProductStore()

  const {
    products: dataBridgeProducts,
    categories: dataBridgeCategories,
    isLoaded
  } = useDataBridgeContext()

  // Use DataBridge data if available, otherwise fallback to store data
  const products = isLoaded && dataBridgeProducts.length > 0 ? dataBridgeProducts : storeProducts
  const categories = isLoaded && dataBridgeCategories.length > 0 ? dataBridgeCategories : storeCategories
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  
  // Get URL parameters
  const categoryParam = searchParams.get('category')
  const searchParam = searchParams.get('search')
  
  useEffect(() => {
    // Only fetch from API if DataBridge is not loaded or has no data
    if (!isLoaded || dataBridgeCategories.length === 0) {
      fetchCategories()
    }
  }, [fetchCategories, isLoaded, dataBridgeCategories.length])
  
  useEffect(() => {
    // Set initial filters from URL
    if (categoryParam) {
      setFilters({ ...filters, category: categoryParam })
    }
    if (searchParam) {
      setSearchQuery(searchParam)
    }
  }, [categoryParam, searchParam])
  
  useEffect(() => {
    fetchProducts()
  }, [fetchProducts, searchQuery, filters])
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchProducts()
  }
  
  const handleSortChange = (value: string) => {
    setFilters({ ...filters, sortBy: value })
  }
  
  const handleCategoryChange = (value: string) => {
    if (value === 'all') {
      setFilters({ ...filters, category: undefined })
    } else {
      setFilters({ ...filters, category: value })
    }
  }
  
  const handlePriceRangeChange = (min: string, max: string) => {
    setFilters({ 
      ...filters, 
      priceRange: { 
        min: min ? parseFloat(min) : undefined, 
        max: max ? parseFloat(max) : undefined 
      } 
    })
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Ürünler</h1>
              <p className="text-gray-600">
                {pagination.total} ürün bulundu
                {filters.category && (
                  <Badge variant="secondary" className="ml-2">
                    {categories.find(c => c.slug === filters.category)?.name || filters.category}
                  </Badge>
                )}
              </p>
            </div>
            
            {/* Search */}
            <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Ürün ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button type="submit">Ara</Button>
            </form>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div className={`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Filtreler</span>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={clearFilters}
                    className="text-xs"
                  >
                    Temizle
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Category Filter */}
                <div>
                  <h3 className="font-medium mb-3">Kategori</h3>
                  <Select value={filters.category || 'all'} onValueChange={handleCategoryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Kategori seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tüm Kategoriler</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.slug}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Price Range */}
                <div>
                  <h3 className="font-medium mb-3">Fiyat Aralığı</h3>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.priceRange?.min || ''}
                      onChange={(e) => handlePriceRangeChange(e.target.value, filters.priceRange?.max?.toString() || '')}
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.priceRange?.max || ''}
                      onChange={(e) => handlePriceRangeChange(filters.priceRange?.min?.toString() || '', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Main Content */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Filtreler
                </Button>
                
                <div className="hidden sm:flex items-center gap-2">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <Select value={filters.sortBy || 'newest'} onValueChange={handleSortChange}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sırala" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">En Yeni</SelectItem>
                  <SelectItem value="oldest">En Eski</SelectItem>
                  <SelectItem value="price-low">Fiyat: Düşük → Yüksek</SelectItem>
                  <SelectItem value="price-high">Fiyat: Yüksek → Düşük</SelectItem>
                  <SelectItem value="name-asc">İsim: A → Z</SelectItem>
                  <SelectItem value="name-desc">İsim: Z → A</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Products Grid */}
            {isLoading ? (
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                  : 'grid-cols-1'
              }`}>
                {[...Array(12)].map((_, index) => (
                  <LoadingCard key={index} />
                ))}
              </div>
            ) : products.length > 0 ? (
              <>
                <div className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                    : 'grid-cols-1'
                }`}>
                  {products.map((product) => (
                    <ProductCard 
                      key={product.id} 
                      product={product}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
                
                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex justify-center mt-12">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        disabled={!pagination.hasPrev}
                        onClick={() => setFilters({ ...filters, page: pagination.page - 1 })}
                      >
                        Önceki
                      </Button>
                      
                      <span className="px-4 py-2 text-sm">
                        {pagination.page} / {pagination.totalPages}
                      </span>
                      
                      <Button
                        variant="outline"
                        disabled={!pagination.hasNext}
                        onClick={() => setFilters({ ...filters, page: pagination.page + 1 })}
                      >
                        Sonraki
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Ürün bulunamadı
                </h3>
                <p className="text-gray-600 mb-6">
                  Arama kriterlerinize uygun ürün bulunamadı. Filtreleri temizleyip tekrar deneyin.
                </p>
                <Button onClick={clearFilters}>
                  Filtreleri Temizle
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
