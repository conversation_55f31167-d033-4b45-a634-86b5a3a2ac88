'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useDataBridge } from '@/lib/hooks/use-data-bridge'

interface DataBridgeContextType {
  isLoaded: boolean
  dataBridge: any
  products: any[]
  categories: any[]
  siteSettings: any
  getProduct: (id: string) => any
  getPageContent: (page: string) => any
  updateSiteSettings: (settings: any) => boolean
  updatePageContent: (page: string, content: any) => boolean
}

const DataBridgeContext = createContext<DataBridgeContextType | undefined>(undefined)

export function DataBridgeProvider({ children }: { children: ReactNode }) {
  const {
    isLoaded,
    dataBridge,
    getProducts,
    getProduct,
    getCategories,
    getSiteSettings,
    getPageContent,
    updateSiteSettings,
    updatePageContent,
  } = useDataBridge()

  const [products, setProducts] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [siteSettings, setSiteSettings] = useState<any>({})

  // Update local state when DataBridge data changes
  useEffect(() => {
    if (isLoaded && dataBridge) {
      setProducts(getProducts())
      setCategories(getCategories())
      setSiteSettings(getSiteSettings())
    }
  }, [isLoaded, dataBridge, getProducts, getCategories, getSiteSettings])

  // Listen for data changes
  useEffect(() => {
    if (!isLoaded) return

    const handleDataChange = () => {
      setProducts(getProducts())
      setCategories(getCategories())
      setSiteSettings(getSiteSettings())
    }

    window.addEventListener('databridge:change', handleDataChange)
    return () => window.removeEventListener('databridge:change', handleDataChange)
  }, [isLoaded, getProducts, getCategories, getSiteSettings])

  const value: DataBridgeContextType = {
    isLoaded,
    dataBridge,
    products,
    categories,
    siteSettings,
    getProduct,
    getPageContent,
    updateSiteSettings,
    updatePageContent,
  }

  return (
    <DataBridgeContext.Provider value={value}>
      {children}
    </DataBridgeContext.Provider>
  )
}

export function useDataBridgeContext() {
  const context = useContext(DataBridgeContext)
  if (context === undefined) {
    throw new Error('useDataBridgeContext must be used within a DataBridgeProvider')
  }
  return context
}
