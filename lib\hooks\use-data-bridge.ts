'use client'

import { useEffect, useState } from 'react'

// Data Bridge Hook for Next.js integration
export function useDataBridge() {
  const [isLoaded, setIsLoaded] = useState(false)
  const [dataBridge, setDataBridge] = useState<any>(null)

  useEffect(() => {
    // Load data bridge script if not already loaded
    if (typeof window !== 'undefined' && !window.dataBridge) {
      const script = document.createElement('script')
      script.src = '/lib/data-bridge.js'
      script.onload = () => {
        setDataBridge(window.dataBridge)
        setIsLoaded(true)
      }
      document.head.appendChild(script)
    } else if (window.dataBridge) {
      setDataBridge(window.dataBridge)
      setIsLoaded(true)
    }

    // Listen for data changes
    const handleDataChange = (event: CustomEvent) => {
      // Force re-render by updating state
      setDataBridge({ ...window.dataBridge })
    }

    window.addEventListener('databridge:change', handleDataChange as EventListener)

    return () => {
      window.removeEventListener('databridge:change', handleData<PERSON>hange as EventListener)
    }
  }, [])

  return {
    isLoaded,
    dataBridge,
    // Convenience methods
    getProducts: () => dataBridge?.getProducts() || [],
    getProduct: (id: string) => dataBridge?.getProduct(id),
    getUsers: () => dataBridge?.getUsers() || [],
    getOrders: () => dataBridge?.getOrders() || [],
    getCategories: () => dataBridge?.getData()?.categories || [],
    getSiteSettings: () => dataBridge?.getSiteSettings() || {},
    getPageContent: (page: string) => dataBridge?.getPageContent(page) || {},
    updateSiteSettings: (settings: any) => dataBridge?.updateSiteSettings(settings),
    updatePageContent: (page: string, content: any) => dataBridge?.updatePageContent(page, content),
    addProduct: (product: any) => dataBridge?.addProduct(product),
    updateProduct: (id: string, product: any) => dataBridge?.updateProduct(id, product),
    deleteProduct: (id: string) => dataBridge?.deleteProduct(id),
  }
}

// Global type declaration
declare global {
  interface Window {
    dataBridge: any
  }
}
