'use client'

import { useEffect, useState } from 'react'

// Data Bridge Hook for Next.js integration
export function useDataBridge() {
  const [isLoaded, setIsLoaded] = useState(false)
  const [dataBridge, setDataBridge] = useState<any>(null)

  useEffect(() => {
    // Check if DataBridge is already available
    if (typeof window !== 'undefined') {
      if (window.dataBridge) {
        setDataBridge(window.dataBridge)
        setIsLoaded(true)
      } else {
        // Wait for DataBridge to be initialized
        const checkDataBridge = () => {
          if (window.dataBridge) {
            setDataBridge(window.dataBridge)
            setIsLoaded(true)
          } else {
            setTimeout(checkDataBridge, 100)
          }
        }
        checkDataBridge()
      }
    }

    // Listen for data changes
    const handleDataChange = (event: CustomEvent) => {
      // Force re-render by updating state
      setDataBridge({ ...window.dataBridge })
    }

    window.addEventListener('databridge:change', handleDataChange as EventListener)

    return () => {
      window.removeEventListener('databridge:change', handleDataChange as EventListener)
    }
  }, [])

  return {
    isLoaded,
    dataBridge,
    // Convenience methods
    getProducts: () => dataBridge?.getProducts() || [],
    getProduct: (id: string) => dataBridge?.getProduct(id),
    getUsers: () => dataBridge?.getUsers() || [],
    getOrders: () => dataBridge?.getOrders() || [],
    getCategories: () => dataBridge?.getData()?.categories || [],
    getSiteSettings: () => dataBridge?.getSiteSettings() || {},
    getPageContent: (page: string) => dataBridge?.getPageContent(page) || {},
    updateSiteSettings: (settings: any) => dataBridge?.updateSiteSettings(settings),
    updatePageContent: (page: string, content: any) => dataBridge?.updatePageContent(page, content),
    addProduct: (product: any) => dataBridge?.addProduct(product),
    updateProduct: (id: string, product: any) => dataBridge?.updateProduct(id, product),
    deleteProduct: (id: string) => dataBridge?.deleteProduct(id),
  }
}

// Global type declaration
declare global {
  interface Window {
    dataBridge: any
  }
}
