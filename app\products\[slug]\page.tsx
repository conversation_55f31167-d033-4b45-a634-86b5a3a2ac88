'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star,
  Truck,
  Shield,
  RotateCcw,
  ArrowLeft,
  Plus,
  Minus,
  Check,
  AlertCircle
} from 'lucide-react'
import { useCartStore } from '@/lib/store/cart-store'
import { useAuthStore } from '@/lib/store/auth-store'
import { useDataBridgeContext } from '@/components/providers/data-bridge-provider'
import { formatPrice } from '@/lib/utils'
import { Product } from '@/types'
import toast from 'react-hot-toast'

export default function ProductDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()
  const { addItem } = useCartStore()
  const { getProduct, isLoaded } = useDataBridgeContext()

  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [isFavorite, setIsFavorite] = useState(false)
  
  useEffect(() => {
    if (params.slug) {
      fetchProduct(params.slug as string)
    }
  }, [params.slug, isLoaded])

  const fetchProduct = async (slug: string) => {
    try {
      // First try to get product from DataBridge
      if (isLoaded) {
        const bridgeProduct = getProduct(slug) ||
          (await new Promise(resolve => {
            // Try to find by slug in products array
            const products = window.dataBridge?.getProducts() || []
            const found = products.find((p: any) => p.slug === slug || p.id === slug)
            resolve(found)
          }))

        if (bridgeProduct) {
          setProduct(bridgeProduct)
          setIsLoading(false)
          return
        }
      }

      // Fallback to API
      const response = await fetch(`/api/products/${slug}`)
      const data = await response.json()

      if (data.success) {
        setProduct(data.data)
      } else {
        router.push('/404')
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      router.push('/404')
    } finally {
      setIsLoading(false)
    }
  }
  
  const handleAddToCart = async () => {
    if (!product) return
    
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=' + encodeURIComponent(window.location.pathname))
      return
    }
    
    setIsAddingToCart(true)
    
    try {
      await addItem({
        id: `${product.id}-${Date.now()}`,
        product,
        quantity,
        selectedVariants: {}
      })
      
      toast.success('Ürün sepete eklendi!')
    } catch (error) {
      toast.error('Ürün sepete eklenirken hata oluştu')
    } finally {
      setIsAddingToCart(false)
    }
  }
  
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (product?.stock || 1)) {
      setQuantity(newQuantity)
    }
  }
  
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href,
        })
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(window.location.href)
        toast.success('Link kopyalandı!')
      }
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link kopyalandı!')
    }
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }
  
  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Ürün bulunamadı</h1>
          <p className="text-gray-600 mb-4">Aradığınız ürün mevcut değil.</p>
          <Button asChild>
            <Link href="/shop">Ürünlere Geri Dön</Link>
          </Button>
        </div>
      </div>
    )
  }
  
  const discountPercentage = product.comparePrice 
    ? Math.round(((product.comparePrice - product.price) / product.comparePrice) * 100)
    : 0
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 mb-6 text-sm">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Geri
            </Button>
            <span className="text-gray-400">/</span>
            <Link href="/shop" className="text-gray-600 hover:text-primary">
              Ürünler
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{product.name}</span>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Product Images */}
            <div className="space-y-4">
              {/* Main Image */}
              <div className="aspect-square bg-white rounded-lg overflow-hidden border">
                <Image
                  src={product.images?.[selectedImageIndex]?.url || '/placeholder-product.jpg'}
                  alt={product.name}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Thumbnail Images */}
              {product.images && product.images.length > 1 && (
                <div className="flex gap-2 overflow-x-auto">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                        selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                      }`}
                    >
                      <Image
                        src={image.url}
                        alt={`${product.name} ${index + 1}`}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Product Info */}
            <div className="space-y-6">
              {/* Title and Price */}
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {product.name}
                </h1>
                
                <div className="flex items-center gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">(24 değerlendirme)</span>
                  </div>
                  
                  <Badge variant="secondary">
                    SKU: {product.sku}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-4">
                  <span className="text-3xl font-bold text-primary">
                    {formatPrice(product.price)}
                  </span>
                  
                  {product.comparePrice && (
                    <>
                      <span className="text-xl text-gray-500 line-through">
                        {formatPrice(product.comparePrice)}
                      </span>
                      <Badge className="bg-red-100 text-red-800">
                        %{discountPercentage} İndirim
                      </Badge>
                    </>
                  )}
                </div>
              </div>
              
              {/* Stock Status */}
              <div className="flex items-center gap-2">
                {product.stock > 0 ? (
                  <>
                    <Check className="h-5 w-5 text-green-600" />
                    <span className="text-green-600 font-medium">
                      Stokta ({product.stock} adet)
                    </span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-5 w-5 text-red-600" />
                    <span className="text-red-600 font-medium">Stokta yok</span>
                  </>
                )}
              </div>
              
              {/* Description */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Ürün Açıklaması</h3>
                <p className="text-gray-700 leading-relaxed">
                  {product.description}
                </p>
              </div>
              
              {/* Quantity and Add to Cart */}
              {product.stock > 0 && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Adet
                    </label>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleQuantityChange(quantity - 1)}
                        disabled={quantity <= 1}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="w-12 text-center font-medium">
                        {quantity}
                      </span>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleQuantityChange(quantity + 1)}
                        disabled={quantity >= product.stock}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex gap-3">
                    <Button
                      onClick={handleAddToCart}
                      disabled={isAddingToCart}
                      className="flex-1"
                      size="lg"
                    >
                      <ShoppingCart className="h-5 w-5 mr-2" />
                      {isAddingToCart ? 'Ekleniyor...' : 'Sepete Ekle'}
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => setIsFavorite(!isFavorite)}
                    >
                      <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current text-red-500' : ''}`} />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={handleShare}
                    >
                      <Share2 className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
              )}
              
              {/* Features */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Truck className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Ücretsiz Kargo</p>
                    <p className="text-xs text-gray-600">150 TL üzeri</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <RotateCcw className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">14 Gün İade</p>
                    <p className="text-xs text-gray-600">Koşulsuz iade</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                  <Shield className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium">Güvenli Ödeme</p>
                    <p className="text-xs text-gray-600">256-bit SSL</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Product Details Tabs */}
          <div className="mt-16">
            <Card>
              <CardContent className="p-8">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-xl font-bold mb-4">Ürün Özellikleri</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <p><strong>Marka:</strong> E-Marketing</p>
                        <p><strong>Model:</strong> {product.name}</p>
                        <p><strong>SKU:</strong> {product.sku}</p>
                      </div>
                      <div>
                        <p><strong>Stok:</strong> {product.stock} adet</p>
                        <p><strong>Kategori:</strong> {product.category?.name}</p>
                        <p><strong>Durum:</strong> {product.isActive ? 'Aktif' : 'Pasif'}</p>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="text-xl font-bold mb-4">Kargo ve İade</h3>
                    <div className="space-y-2 text-gray-700">
                      <p>• 150 TL üzeri siparişlerde ücretsiz kargo</p>
                      <p>• İstanbul içi 1-2 iş günü, Türkiye geneli 2-5 iş günü teslimat</p>
                      <p>• 14 gün içinde koşulsuz iade hakkı</p>
                      <p>• Ürün hasarlı gelirse kargo ücreti tarafımızca karşılanır</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
