import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password, name, setupKey } = body
    
    // Check setup key for security
    if (setupKey !== process.env.ADMIN_SETUP_KEY) {
      return NextResponse.json(
        { success: false, error: 'Geçersiz kurulum anahtarı' },
        { status: 403 }
      )
    }
    
    const supabase = createServerClient()
    
    // Check if admin already exists
    const { data: existingAdmin } = await supabase
      .from('users')
      .select('id')
      .eq('role', 'ADMIN')
      .single()
    
    if (existingAdmin) {
      return NextResponse.json(
        { success: false, error: 'Ad<PERSON> kullan<PERSON>ı zaten mevcut' },
        { status: 400 }
      )
    }
    
    // Create admin user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        name,
        role: 'ADMIN'
      }
    })
    
    if (authError) {
      return NextResponse.json(
        { success: false, error: authError.message },
        { status: 500 }
      )
    }
    
    if (!authData.user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oluşturulamadı' },
        { status: 500 }
      )
    }
    
    // Create user record in users table
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        name,
        role: 'ADMIN',
        is_active: true,
      })
    
    if (userError) {
      // Cleanup auth user if database insert fails
      await supabase.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json(
        { success: false, error: userError.message },
        { status: 500 }
      )
    }
    
    // Create profile record
    await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
      })
    
    return NextResponse.json({
      success: true,
      message: 'Admin kullanıcısı başarıyla oluşturuldu',
      data: {
        id: authData.user.id,
        email,
        name,
        role: 'ADMIN'
      }
    })
    
  } catch (error) {
    console.error('Error creating admin user:', error)
    return NextResponse.json(
      { success: false, error: 'Admin kullanıcısı oluşturulurken hata oluştu' },
      { status: 500 }
    )
  }
}

// Get admin setup status
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    
    // Check if admin exists
    const { data: adminExists } = await supabase
      .from('users')
      .select('id')
      .eq('role', 'ADMIN')
      .single()
    
    return NextResponse.json({
      success: true,
      data: {
        adminExists: !!adminExists,
        setupRequired: !adminExists
      }
    })
    
  } catch (error) {
    console.error('Error checking admin setup:', error)
    return NextResponse.json(
      { success: false, error: 'Admin durumu kontrol edilirken hata oluştu' },
      { status: 500 }
    )
  }
}
