'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  MapPin, 
  Edit, 
  Trash2, 
  Home, 
  Building2,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/lib/store/auth-store'
import { useRouter } from 'next/navigation'

interface Address {
  id: string
  title: string
  type: 'home' | 'work' | 'other'
  fullName: string
  phone: string
  address: string
  city: string
  district: string
  postalCode: string
  isDefault: boolean
}

export default function AddressesPage() {
  const { user, isAuthenticated } = useAuthStore()
  const router = useRouter()
  const [addresses, setAddresses] = useState<Address[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/profile/addresses')
      return
    }
    
    // Mock data for demonstration
    const mockAddresses: Address[] = [
      {
        id: '1',
        title: 'Ev Adresim',
        type: 'home',
        fullName: 'John Doe',
        phone: '+90 ************',
        address: 'Atatürk Mahallesi, Cumhuriyet Caddesi No: 123/5',
        city: 'İstanbul',
        district: 'Kadıköy',
        postalCode: '34710',
        isDefault: true
      },
      {
        id: '2',
        title: 'İş Adresim',
        type: 'work',
        fullName: 'John Doe',
        phone: '+90 ************',
        address: 'İş Merkezi, Ofis Blok A Kat: 5 No: 12',
        city: 'İstanbul',
        district: 'Şişli',
        postalCode: '34394',
        isDefault: false
      }
    ]
    
    setAddresses(mockAddresses)
    setIsLoading(false)
  }, [isAuthenticated, router])

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'home':
        return <Home className="h-4 w-4" />
      case 'work':
        return <Building2 className="h-4 w-4" />
      default:
        return <MapPin className="h-4 w-4" />
    }
  }

  const getAddressTypeLabel = (type: string) => {
    switch (type) {
      case 'home':
        return 'Ev'
      case 'work':
        return 'İş'
      default:
        return 'Diğer'
    }
  }

  if (!isAuthenticated) {
    return null
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/profile">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Adreslerim</h1>
              <p className="text-gray-600">Teslimat adreslerinizi yönetin</p>
            </div>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Yeni Adres Ekle
          </Button>
        </div>

        {/* Addresses Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {addresses.map((address) => (
            <Card key={address.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getAddressIcon(address.type)}
                    <CardTitle className="text-lg">{address.title}</CardTitle>
                  </div>
                  <div className="flex items-center space-x-1">
                    {address.isDefault && (
                      <Badge variant="secondary" className="text-xs">
                        Varsayılan
                      </Badge>
                    )}
                    <Badge variant="outline" className="text-xs">
                      {getAddressTypeLabel(address.type)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium text-gray-900">{address.fullName}</p>
                  <p className="text-sm text-gray-600">{address.phone}</p>
                </div>
                
                <div className="text-sm text-gray-600">
                  <p>{address.address}</p>
                  <p>{address.district} / {address.city}</p>
                  <p>Posta Kodu: {address.postalCode}</p>
                </div>
                
                <div className="flex items-center justify-between pt-3 border-t">
                  <Button variant="outline" size="sm">
                    <Edit className="h-3 w-3 mr-1" />
                    Düzenle
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                    <Trash2 className="h-3 w-3 mr-1" />
                    Sil
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {addresses.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Henüz adres eklenmemiş
              </h3>
              <p className="text-gray-600 mb-6">
                Hızlı teslimat için adres bilgilerinizi ekleyin
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                İlk Adresimi Ekle
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
