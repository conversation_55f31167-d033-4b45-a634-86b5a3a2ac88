import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Yetkisiz erişim' },
        { status: 403 }
      )
    }
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const category = searchParams.get('category')
    const lowStock = searchParams.get('lowStock') === 'true'
    
    const skip = (page - 1) * limit
    
    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        images:product_images(*)
      `, { count: 'exact' })
    
    if (search) {
      query = query.or(`name.ilike.%${search}%,sku.ilike.%${search}%`)
    }
    
    if (category) {
      query = query.eq('category_id', category)
    }
    
    if (lowStock) {
      query = query.lt('stock', 10)
    }
    
    const { data: products, count, error } = await query
      .order('created_at', { ascending: false })
      .range(skip, skip + limit - 1)
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    const total = count || 0
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      success: true,
      data: products || [],
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })
    
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { success: false, error: 'Ürünler yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Yetkisiz erişim' },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    const {
      name,
      description,
      price,
      comparePrice,
      sku,
      stock,
      categoryId,
      images,
      isActive,
      isFeatured,
      weight,
      dimensions,
      tags
    } = body
    
    // Validate required fields
    if (!name || !price || !categoryId) {
      return NextResponse.json(
        { success: false, error: 'Ad, fiyat ve kategori zorunludur' },
        { status: 400 }
      )
    }
    
    // Generate slug
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
    
    // Check if slug already exists
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('slug', slug)
      .single()
    
    if (existingProduct) {
      return NextResponse.json(
        { success: false, error: 'Bu isimde bir ürün zaten mevcut' },
        { status: 400 }
      )
    }
    
    // Create product
    const { data: product, error } = await supabase
      .from('products')
      .insert({
        name,
        slug,
        description,
        price,
        compare_price: comparePrice,
        sku,
        stock: stock || 0,
        category_id: categoryId,
        is_active: isActive !== false,
        is_featured: isFeatured || false,
        weight,
        dimensions,
        tags,
        status: 'ACTIVE',
      })
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    
    // Add images if provided
    if (images && images.length > 0) {
      const imageInserts = images.map((image: any, index: number) => ({
        product_id: product.id,
        url: image.url,
        alt: image.alt || name,
        sort_order: index,
      }))
      
      await supabase
        .from('product_images')
        .insert(imageInserts)
    }
    
    return NextResponse.json({
      success: true,
      data: product,
      message: 'Ürün başarıyla oluşturuldu'
    })
    
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { success: false, error: 'Ürün oluşturulurken hata oluştu' },
      { status: 500 }
    )
  }
}
