import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = Number(searchParams.get('limit')) || 20
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        success: false,
        error: 'Arama terimi en az 2 karakter olmalıdır'
      }, { status: 400 })
    }
    
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        status: 'ACTIVE',
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { sku: { contains: query, mode: 'insensitive' } },
          {
            category: {
              name: { contains: query, mode: 'insensitive' }
            }
          }
        ]
      },
      include: {
        category: true,
        images: {
          orderBy: { sortOrder: 'asc' }
        },
        reviews: {
          select: {
            rating: true
          }
        }
      },
      orderBy: [
        { isFeatured: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit,
    })
    
    // Transform products with average rating
    const transformedProducts = products.map(product => ({
      ...product,
      averageRating: product.reviews.length > 0 
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
        : 0,
      reviewCount: product.reviews.length,
      reviews: undefined, // Remove reviews from response
    }))
    
    return NextResponse.json({
      success: true,
      data: {
        data: transformedProducts,
        pagination: {
          page: 1,
          limit,
          total: transformedProducts.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        }
      }
    })
    
  } catch (error) {
    console.error('Error searching products:', error)
    return NextResponse.json(
      { success: false, error: 'Arama yapılırken hata oluştu' },
      { status: 500 }
    )
  }
}
