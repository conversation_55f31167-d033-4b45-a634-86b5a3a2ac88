'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { useDataBridgeContext } from '@/components/providers/data-bridge-provider'
import {
  Smartphone,
  Shirt,
  Home,
  Dumbbell,
  BookOpen,
  ArrowRight,
  Package
} from 'lucide-react'

interface Category {
  id: string
  name: string
  slug: string
  description?: string
  productCount?: number
  image?: string
}

const categoryIcons = {
  'elektronik': Smartphone,
  'giyim': Shirt,
  'ev-yasam': Home,
  'spor-outdoor': Dumbbell,
  'kitap-hobi': BookOpen,
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { categories: dataBridgeCategories, products, isLoaded } = useDataBridgeContext()

  useEffect(() => {
    if (isLoaded && dataBridgeCategories.length > 0) {
      // Use DataBridge categories and calculate product counts
      const categoriesWithCounts = dataBridgeCategories.map((cat: any) => ({
        ...cat,
        productCount: products.filter((p: any) => p.categoryId === cat.id).length
      }))
      setCategories(categoriesWithCounts)
      setIsLoading(false)
    } else {
      fetchCategories()
    }
  }, [isLoaded, dataBridgeCategories, products])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories?includeProducts=true')
      const data = await response.json()

      if (data.success) {
        setCategories(data.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">Kategoriler</h1>
          <p className="text-xl max-w-2xl mx-auto">
            İhtiyacınız olan her şeyi kolayca bulabileceğiniz kategorilerimizi keşfedin
          </p>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {categories.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {categories.map((category) => {
                const IconComponent = categoryIcons[category.slug as keyof typeof categoryIcons] || Package
                
                return (
                  <Link key={category.id} href={`/shop?category=${category.slug}`}>
                    <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer h-full">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                            <IconComponent className="h-6 w-6 text-primary" />
                          </div>
                          <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-primary group-hover:translate-x-1 transition-all" />
                        </div>
                        
                        <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                          {category.name}
                        </h3>
                        
                        {category.description && (
                          <p className="text-gray-600 mb-4 text-sm">
                            {category.description}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <Badge variant="secondary" className="text-xs">
                            {category.productCount || 0} ürün
                          </Badge>
                          <span className="text-sm text-primary font-medium group-hover:underline">
                            Ürünleri Gör
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Henüz kategori yok
              </h3>
              <p className="text-gray-600 mb-6">
                Yakında kategorilerimizi ekleyeceğiz.
              </p>
              <Link 
                href="/shop"
                className="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Tüm Ürünleri Görüntüle
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* Popular Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Popüler Kategoriler</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: 'Telefon', icon: Smartphone, href: '/shop?category=elektronik&subcategory=telefon' },
              { name: 'Laptop', icon: Package, href: '/shop?category=elektronik&subcategory=laptop' },
              { name: 'Giyim', icon: Shirt, href: '/shop?category=giyim' },
              { name: 'Ayakkabı', icon: Package, href: '/shop?category=giyim&subcategory=ayakkabi' },
              { name: 'Ev Dekor', icon: Home, href: '/shop?category=ev-yasam&subcategory=dekor' },
              { name: 'Spor', icon: Dumbbell, href: '/shop?category=spor-outdoor' },
            ].map((item, index) => (
              <Link key={index} href={item.href}>
                <Card className="group hover:shadow-md transition-all duration-300 cursor-pointer">
                  <CardContent className="p-4 text-center">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary/10 transition-colors">
                      <item.icon className="h-6 w-6 text-gray-600 group-hover:text-primary transition-colors" />
                    </div>
                    <h3 className="text-sm font-medium group-hover:text-primary transition-colors">
                      {item.name}
                    </h3>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Aradığınızı Bulamadınız mı?</h2>
          <p className="text-xl mb-8 text-gray-300">
            Binlerce ürün arasından istediğinizi bulmak için arama yapın
          </p>
          <Link 
            href="/shop"
            className="inline-block bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
          >
            Tüm Ürünleri Keşfet
          </Link>
        </div>
      </section>
    </div>
  )
}
