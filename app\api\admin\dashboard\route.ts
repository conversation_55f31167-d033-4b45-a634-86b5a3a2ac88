import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'Kullanıcı oturumu bulunamadı' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Yetkisiz erişim' },
        { status: 403 }
      )
    }
    
    // Fetch dashboard statistics
    const [
      { count: totalProducts },
      { count: totalOrders },
      { count: totalUsers },
      { data: revenueData },
      { count: pendingOrders },
      { count: lowStockProducts },
      { data: recentOrders },
      { data: topProducts }
    ] = await Promise.all([
      // Total products
      supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true),
      
      // Total orders
      supabase
        .from('orders')
        .select('*', { count: 'exact', head: true }),
      
      // Total users
      supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'USER'),
      
      // Total revenue
      supabase
        .from('orders')
        .select('total_amount')
        .eq('payment_status', 'PAID'),
      
      // Pending orders
      supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'PENDING'),
      
      // Low stock products (stock < 10)
      supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .lt('stock', 10),
      
      // Recent orders
      supabase
        .from('orders')
        .select(`
          *,
          user:users(name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(10),
      
      // Top products (mock data for now)
      supabase
        .from('products')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(10)
    ])
    
    // Calculate total revenue
    const totalRevenue = revenueData?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0
    
    // Mock sales count for top products (in real app, this would come from order_items)
    const topProductsWithSales = topProducts?.map((product, index) => ({
      ...product,
      salesCount: Math.floor(Math.random() * 100) + 1
    })).sort((a, b) => b.salesCount - a.salesCount) || []
    
    const dashboardData = {
      totalProducts: totalProducts || 0,
      totalOrders: totalOrders || 0,
      totalUsers: totalUsers || 0,
      totalRevenue,
      pendingOrders: pendingOrders || 0,
      lowStockProducts: lowStockProducts || 0,
      recentOrders: recentOrders || [],
      topProducts: topProductsWithSales
    }
    
    return NextResponse.json({
      success: true,
      data: dashboardData
    })
    
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
    return NextResponse.json(
      { success: false, error: 'Dashboard verileri yüklenirken hata oluştu' },
      { status: 500 }
    )
  }
}
