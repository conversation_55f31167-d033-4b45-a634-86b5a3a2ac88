import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RotateCcw, Clock, Package, CreditCard, AlertCircle, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function ReturnsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <RotateCcw className="h-12 w-12 mr-4" />
            <h1 className="text-4xl font-bold">İade ve Değişim Politikası</h1>
          </div>
          <p className="text-xl max-w-3xl mx-auto">
            Memnuniyetiniz bizim önceliğimizdir. İade ve değişim işlemlerinizi 
            kolayca gerçekleştirebilirsiniz.
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* Quick Info */}
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="text-center">
              <CardContent className="p-6">
                <Clock className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">14 Gün</h3>
                <p className="text-gray-600">İade süresi</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Package className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Ücretsiz</h3>
                <p className="text-gray-600">Hatalı ürün iadesi</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <CreditCard className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">5-10 Gün</h3>
                <p className="text-gray-600">Para iade süresi</p>
              </CardContent>
            </Card>
          </div>

          {/* Return Conditions */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <CheckCircle className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">İade Koşulları</h2>
              </div>
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-green-600">İade Edilebilir Ürünler</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-2">
                      <li>Orijinal ambalajında olan ürünler</li>
                      <li>Kullanılmamış ve hasarsız ürünler</li>
                      <li>Etiketleri çıkarılmamış giyim ürünleri</li>
                      <li>Açılmamış elektronik ürünler</li>
                      <li>Temiz ve hijyenik durumda olan ürünler</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-red-600">İade Edilemeyen Ürünler</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-2">
                      <li>Hijyen gerektiren ürünler (iç giyim, kozmetik)</li>
                      <li>Kişiye özel üretilen ürünler</li>
                      <li>Bozulabilir gıda ürünleri</li>
                      <li>Dijital içerikler ve yazılımlar</li>
                      <li>Mühürlü ürünlerin mührü açılmışsa</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-blue-900 mb-1">Önemli Not</h4>
                      <p className="text-blue-700 text-sm">
                        İade edilecek ürünler temiz, hasarsız ve satılabilir durumda olmalıdır. 
                        Kullanım izleri bulunan ürünler iade kabul edilmeyebilir.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Return Process */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <RotateCcw className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">İade Süreci</h2>
              </div>
              <div className="space-y-6">
                <div className="grid gap-6">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      1
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">İade Talebinde Bulunun</h3>
                      <p className="text-gray-700">
                        "Siparişlerim" sayfasından veya müşteri hizmetlerimizi arayarak 
                        iade talebinde bulunun. İade sebebini belirtin.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      2
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">İade Onayı Alın</h3>
                      <p className="text-gray-700">
                        İade talebiniz değerlendirilir ve 24 saat içinde size geri dönüş yapılır. 
                        Onay sonrası iade kodu gönderilir.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      3
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Ürünü Paketleyin</h3>
                      <p className="text-gray-700">
                        Ürünü orijinal ambalajı ile birlikte güvenli şekilde paketleyin. 
                        İade kodunu pakete ekleyin.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      4
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Kargoya Verin</h3>
                      <p className="text-gray-700">
                        Ürünü anlaşmalı kargo firması ile gönderin. 
                        Kargo takip numarasını saklayın.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                      5
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Para İadesi</h3>
                      <p className="text-gray-700">
                        Ürün kontrolü sonrası 5-10 iş günü içinde 
                        ödeme yaptığınız kartınıza para iadesi yapılır.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Return Costs */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <CreditCard className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">İade Masrafları</h2>
              </div>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-green-800 mb-2">
                      Ücretsiz İade
                    </h3>
                    <ul className="list-disc list-inside text-green-700 space-y-1">
                      <li>Hatalı ürün gönderimi</li>
                      <li>Hasarlı ürün teslimatı</li>
                      <li>Eksik ürün gönderimi</li>
                      <li>Yanlış ürün teslimatı</li>
                    </ul>
                  </div>
                  
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-orange-800 mb-2">
                      Ücretli İade
                    </h3>
                    <ul className="list-disc list-inside text-orange-700 space-y-1">
                      <li>Müşteri kaynaklı iade (beğenmeme)</li>
                      <li>Yanlış beden/renk seçimi</li>
                      <li>Fikir değiştirme</li>
                      <li>Kargo ücreti: 29,90 TL</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold mb-2">Özel Durumlar</h3>
                  <ul className="list-disc list-inside text-gray-700 space-y-1">
                    <li>Büyük/ağır ürünlerde özel kargo ücreti uygulanır</li>
                    <li>Kurumsal müşteriler için özel iade koşulları geçerlidir</li>
                    <li>Kampanyalı ürünlerde farklı iade koşulları olabilir</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Exchange Policy */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Package className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Değişim Politikası</h2>
              </div>
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">
                  Ürün değişimi sadece aynı ürünün farklı beden, renk veya modeliyle yapılabilir. 
                  Fiyat farkı varsa ek ödeme talep edilir veya fark iade edilir.
                </p>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Değişim Koşulları</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Aynı ürün kategorisinde değişim</li>
                      <li>Stokta bulunma durumu</li>
                      <li>14 gün içinde talep</li>
                      <li>Orijinal durumda olma</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-2">Değişim Süreci</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Değişim talebinde bulunun</li>
                      <li>Ürünü iade edin</li>
                      <li>Yeni ürün gönderimi</li>
                      <li>Fiyat farkı hesaplaması</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Refund Information */}
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <CreditCard className="h-6 w-6 text-primary mr-3" />
                <h2 className="text-2xl font-bold">Para İadesi</h2>
              </div>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-3">İade Süreleri</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Kredi kartı: 5-10 iş günü</li>
                      <li>Banka kartı: 3-7 iş günü</li>
                      <li>Havale/EFT: 1-3 iş günü</li>
                      <li>Kapıda ödeme: Havale ile iade</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-3">İade Yöntemleri</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Aynı ödeme yöntemi ile iade</li>
                      <li>Banka hesabına havale</li>
                      <li>Hediye çeki (talep halinde)</li>
                      <li>Kredi olarak hesapta tutma</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-yellow-900 mb-1">Dikkat</h4>
                      <p className="text-yellow-700 text-sm">
                        Para iadesi, ürünün depomuzda kontrol edilip onaylandıktan sonra başlatılır. 
                        Banka işlem süreleri değişkenlik gösterebilir.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact and Support */}
          <Card>
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-6">İade Desteği</h2>
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">
                  İade işlemlerinizle ilgili herhangi bir sorunuz varsa, 
                  müşteri hizmetlerimizle iletişime geçebilirsiniz.
                </p>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold mb-2">İletişim Bilgileri</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Telefon:</strong> (0212) 123 45 67</p>
                      <p><strong>E-posta:</strong> <EMAIL></p>
                      <p><strong>WhatsApp:</strong> (0532) 123 45 67</p>
                      <p><strong>Çalışma Saatleri:</strong> 09:00 - 18:00</p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold mb-2">Online İşlemler</h3>
                    <div className="space-y-2">
                      <Button asChild className="w-full" size="sm">
                        <Link href="/orders">
                          Siparişlerim
                        </Link>
                      </Button>
                      <Button asChild variant="outline" className="w-full" size="sm">
                        <Link href="/contact">
                          İletişim Formu
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
