'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { useAuthStore } from '@/lib/store/auth-store'
import { formatPrice, formatDate } from '@/lib/utils'
import { 
  Package, 
  Calendar,
  CreditCard,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Eye
} from 'lucide-react'
import Link from 'next/link'

interface Order {
  id: string
  orderNumber: string
  status: string
  paymentStatus: string
  totalAmount: number
  createdAt: string
  orderItems: Array<{
    id: string
    quantity: number
    price: number
    product: {
      name: string
      images?: Array<{ url: string }>
    }
  }>
}

const statusConfig = {
  PENDING: { label: 'Beklemede', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  CONFIRMED: { label: 'Onaylandı', color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
  PROCESSING: { label: 'Ha<PERSON>ırlanıyor', color: 'bg-purple-100 text-purple-800', icon: Package },
  SHIPPED: { label: 'Kargoda', color: 'bg-indigo-100 text-indigo-800', icon: Truck },
  DELIVERED: { label: 'Teslim Edildi', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  CANCELLED: { label: 'İptal Edildi', color: 'bg-red-100 text-red-800', icon: XCircle },
}

const paymentStatusConfig = {
  PENDING: { label: 'Beklemede', color: 'bg-yellow-100 text-yellow-800' },
  PAID: { label: 'Ödendi', color: 'bg-green-100 text-green-800' },
  FAILED: { label: 'Başarısız', color: 'bg-red-100 text-red-800' },
  REFUNDED: { label: 'İade Edildi', color: 'bg-gray-100 text-gray-800' },
}

export default function OrdersPage() {
  const router = useRouter()
  const { user, isAuthenticated, isLoading } = useAuthStore()
  const [orders, setOrders] = useState<Order[]>([])
  const [ordersLoading, setOrdersLoading] = useState(true)
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login?redirect=/orders')
    }
  }, [isAuthenticated, isLoading, router])
  
  useEffect(() => {
    if (isAuthenticated) {
      fetchOrders()
    }
  }, [isAuthenticated])
  
  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      const data = await response.json()
      
      if (data.success) {
        setOrders(data.data)
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
    } finally {
      setOrdersLoading(false)
    }
  }
  
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }
  
  if (!isAuthenticated || !user) {
    return null
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Siparişlerim</h1>
          <p className="text-gray-600">Geçmiş siparişlerinizi görüntüleyin ve takip edin</p>
        </div>
        
        {ordersLoading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : orders.length > 0 ? (
          <div className="space-y-6">
            {orders.map((order) => {
              const statusInfo = statusConfig[order.status as keyof typeof statusConfig]
              const paymentInfo = paymentStatusConfig[order.paymentStatus as keyof typeof paymentStatusConfig]
              const StatusIcon = statusInfo?.icon || Package
              
              return (
                <Card key={order.id}>
                  <CardHeader>
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Package className="h-5 w-5" />
                          Sipariş #{order.orderNumber}
                        </CardTitle>
                        <CardDescription className="flex items-center gap-4 mt-2">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {formatDate(order.createdAt)}
                          </span>
                          <span className="flex items-center gap-1">
                            <CreditCard className="h-4 w-4" />
                            {formatPrice(order.totalAmount)}
                          </span>
                        </CardDescription>
                      </div>
                      
                      <div className="flex flex-col sm:items-end gap-2">
                        <div className="flex gap-2">
                          <Badge className={statusInfo?.color}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {statusInfo?.label}
                          </Badge>
                          <Badge className={paymentInfo?.color}>
                            {paymentInfo?.label}
                          </Badge>
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/orders/${order.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            Detaylar
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      <h4 className="font-medium text-sm text-gray-700">
                        Sipariş İçeriği ({order.orderItems.length} ürün)
                      </h4>
                      <div className="grid gap-3">
                        {order.orderItems.slice(0, 3).map((item) => (
                          <div key={item.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                            <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                              <Package className="h-6 w-6 text-gray-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">{item.product.name}</p>
                              <p className="text-xs text-gray-600">
                                {item.quantity} adet × {formatPrice(item.price)}
                              </p>
                            </div>
                            <div className="text-sm font-medium">
                              {formatPrice(item.quantity * item.price)}
                            </div>
                          </div>
                        ))}
                        
                        {order.orderItems.length > 3 && (
                          <div className="text-center py-2">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/orders/${order.id}`}>
                                +{order.orderItems.length - 3} ürün daha
                              </Link>
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Henüz siparişiniz yok
              </h3>
              <p className="text-gray-600 mb-6">
                Alışverişe başlayarak ilk siparişinizi verin
              </p>
              <Button asChild>
                <Link href="/shop">
                  Alışverişe Başla
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
