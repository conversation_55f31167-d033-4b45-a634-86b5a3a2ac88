<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Marketing - Modern E-Ticaret Sitesi</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#3b82f6',
                            foreground: '#ffffff'
                        },
                        secondary: {
                            DEFAULT: '#f1f5f9',
                            foreground: '#0f172a'
                        },
                        muted: {
                            DEFAULT: '#f1f5f9',
                            foreground: '#64748b'
                        },
                        card: {
                            DEFAULT: '#ffffff',
                            foreground: '#0f172a'
                        },
                        border: '#e2e8f0'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="./lib/data-bridge.js"></script>
    <style>
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary:hover {
            background-color: #2563eb;
        }
        .card {
            background-color: white;
            color: #0f172a;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .aspect-square {
            aspect-ratio: 1 / 1;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-8">
                    <h1 class="text-2xl font-bold text-primary">E-Marketing</h1>
                    <div class="hidden md:flex space-x-6">
                        <a href="#home" class="text-gray-700 hover:text-primary transition-colors">Ana Sayfa</a>
                        <a href="#shop" class="text-gray-700 hover:text-primary transition-colors">Ürünler</a>
                        <a href="#categories" class="text-gray-700 hover:text-primary transition-colors">Kategoriler</a>
                        <a href="#about" class="text-gray-700 hover:text-primary transition-colors">Hakkımızda</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative hidden md:block">
                        <input type="text" id="search-input" placeholder="Ürün ara..."
                               onkeyup="searchProducts(this.value)"
                               class="w-64 pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <button class="relative p-2 text-gray-700 hover:text-primary">
                        <i class="fas fa-heart text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                    </button>
                    <button class="cart-button relative p-2 text-gray-700 hover:text-primary" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style="display: none;">0</span>
                    </button>
                    <button class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                        Giriş Yap
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Content Container -->
    <div id="page-content">
        <!-- Home Page -->
        <div id="home" class="page-section">
            <!-- Hero Section -->
            <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
                <div class="container mx-auto px-4 text-center">
                    <h1 class="text-5xl font-bold mb-6">E-Marketing'e Hoş Geldiniz</h1>
                    <p class="text-xl mb-8 max-w-2xl mx-auto">
                        Modern, güvenli ve kullanıcı dostu e-ticaret deneyimi için doğru adrestesiniz.
                        Binlerce ürün arasından seçim yapın ve hızlı teslimatın keyfini çıkarın.
                    </p>
                    <div class="space-x-4">
                        <button onclick="showPage('shop')" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-md font-medium transition-colors">
                            Alışverişe Başla
                        </button>
                        <button class="border-white text-white hover:bg-white hover:text-blue-600 border px-8 py-3 rounded-md font-medium transition-colors">
                            Üye Ol
                        </button>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="py-16 bg-gray-50">
                <div class="container mx-auto px-4">
                    <h2 class="text-3xl font-bold text-center mb-12">Neden E-Marketing?</h2>
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <div class="card text-center p-6">
                            <i class="fas fa-shopping-bag text-4xl text-blue-600 mb-4"></i>
                            <h3 class="text-xl font-semibold mb-2">Geniş Ürün Yelpazesi</h3>
                            <p class="text-gray-600">Binlerce ürün arasından istediğinizi kolayca bulun ve karşılaştırın.</p>
                        </div>
                        <div class="card text-center p-6">
                            <i class="fas fa-shield-alt text-4xl text-green-600 mb-4"></i>
                            <h3 class="text-xl font-semibold mb-2">Güvenli Alışveriş</h3>
                            <p class="text-gray-600">SSL sertifikası ve güvenli ödeme sistemleri ile korumalı alışveriş.</p>
                        </div>
                        <div class="card text-center p-6">
                            <i class="fas fa-shipping-fast text-4xl text-purple-600 mb-4"></i>
                            <h3 class="text-xl font-semibold mb-2">Hızlı Teslimat</h3>
                            <p class="text-gray-600">Siparişleriniz en kısa sürede kapınıza teslim edilir.</p>
                        </div>
                        <div class="card text-center p-6">
                            <i class="fas fa-chart-line text-4xl text-orange-600 mb-4"></i>
                            <h3 class="text-xl font-semibold mb-2">En İyi Fiyatlar</h3>
                            <p class="text-gray-600">Rekabetçi fiyatlar ve özel indirimlerle tasarruf edin.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Featured Products -->
            <section class="py-16 bg-white">
                <div class="container mx-auto px-4">
                    <h2 class="text-3xl font-bold text-center mb-12">Öne Çıkan Ürünler</h2>
                    <div id="featured-products" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Dynamic content will be loaded here -->
                    </div>
                    <div class="text-center mt-8">
                        <button onclick="showPage('shop')" class="bg-primary text-white px-8 py-3 rounded-md hover:bg-primary/90 transition-colors">
                            Tüm Ürünleri Gör
                        </button>
                    </div>
                </div>
            </section>
        </div>

        <!-- Shop Page -->
        <div id="shop" class="page-section hidden">
            <!-- Header -->
            <div class="bg-white border-b">
                <div class="container mx-auto px-4 py-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Tüm Ürünler</h1>
                    <p class="text-gray-600">Binlerce ürün arasından istediğinizi bulun ve güvenle satın alın.</p>
                </div>
            </div>

            <!-- Main Content -->
            <div class="container mx-auto px-4 py-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    <!-- Filters Sidebar -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-4 space-y-4">
                            <!-- Search -->
                            <div class="relative">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" placeholder="Ürün ara..." class="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>

                            <!-- Sort -->
                            <div class="flex items-center justify-between gap-4">
                                <select class="flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option>En Yeni</option>
                                    <option>En Eski</option>
                                    <option>Fiyat (Düşük → Yüksek)</option>
                                    <option>Fiyat (Yüksek → Düşük)</option>
                                    <option>İsim (A → Z)</option>
                                    <option>İsim (Z → A)</option>
                                </select>
                            </div>

                            <!-- Categories Filter -->
                            <div class="card p-4">
                                <h3 class="font-semibold mb-3">Kategoriler</h3>
                                <div class="space-y-2">
                                    <label class="flex items-center space-x-2 cursor-pointer">
                                        <input type="radio" name="category" value="all" checked
                                               onchange="filterByCategory('all')"
                                               class="rounded border-gray-300 text-primary focus:ring-primary">
                                        <span class="text-sm">Tümü</span>
                                    </label>
                                    <div id="category-filters">
                                        <!-- Dynamic category filters will be loaded here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Price Range Filter -->
                            <div class="card p-4">
                                <h3 class="font-semibold mb-3">Fiyat Aralığı</h3>
                                <div class="space-y-4">
                                    <input type="range" min="0" max="10000" value="5000" class="w-full">
                                    <div class="flex items-center justify-between text-sm text-gray-600">
                                        <span>₺0</span>
                                        <span>₺10.000</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Other Filters -->
                            <div class="card p-4">
                                <h3 class="font-semibold mb-3">Diğer Filtreler</h3>
                                <div class="space-y-2">
                                    <label class="flex items-center space-x-2 cursor-pointer">
                                        <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                                        <span class="text-sm">Sadece Stokta Olanlar</span>
                                    </label>
                                    <label class="flex items-center space-x-2 cursor-pointer">
                                        <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                                        <span class="text-sm">Öne Çıkan Ürünler</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="lg:col-span-3">
                        <div id="shop-products" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            <!-- Dynamic content will be loaded here -->
                        </div>

                        <!-- Pagination -->
                        <div class="flex items-center justify-center space-x-2">
                            <button class="px-3 py-2 border rounded-md hover:bg-gray-50 disabled:opacity-50" disabled>
                                <i class="fas fa-chevron-left mr-1"></i>Önceki
                            </button>
                            <button class="px-3 py-2 bg-primary text-white rounded-md">1</button>
                            <button class="px-3 py-2 border rounded-md hover:bg-gray-50">2</button>
                            <button class="px-3 py-2 border rounded-md hover:bg-gray-50">3</button>
                            <span class="px-2">...</span>
                            <button class="px-3 py-2 border rounded-md hover:bg-gray-50">10</button>
                            <button class="px-3 py-2 border rounded-md hover:bg-gray-50">
                                Sonraki<i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>

                        <!-- Results Info -->
                        <div class="text-center text-sm text-gray-600 mt-4">
                            <p>1 - 12 arası, toplam 120 ürün gösteriliyor</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Detail Page -->
        <div id="product" class="page-section hidden">
            <div class="container mx-auto px-4 py-8">
                <!-- Breadcrumb -->
                <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                    <a href="#" onclick="showPage('home')" class="hover:text-primary">Ana Sayfa</a>
                    <span>/</span>
                    <a href="#" onclick="showPage('shop')" class="hover:text-primary">Ürünler</a>
                    <span>/</span>
                    <a href="#" class="hover:text-primary">Elektronik</a>
                    <span>/</span>
                    <span class="text-gray-900">Gaming Laptop 16GB RAM</span>
                </nav>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
                    <!-- Product Images -->
                    <div class="space-y-4">
                        <!-- Main Image -->
                        <div class="relative aspect-square overflow-hidden rounded-lg bg-white">
                            <img src="https://via.placeholder.com/600x600/3b82f6/ffffff?text=Gaming+Laptop" alt="Gaming Laptop" class="w-full h-full object-cover">
                            <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-md font-medium">%25 İndirim</div>
                        </div>

                        <!-- Thumbnail Images -->
                        <div class="flex space-x-2 overflow-x-auto">
                            <button class="relative flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 border-primary">
                                <img src="https://via.placeholder.com/80x80/3b82f6/ffffff?text=1" alt="Görsel 1" class="w-full h-full object-cover">
                            </button>
                            <button class="relative flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 border-gray-200">
                                <img src="https://via.placeholder.com/80x80/10b981/ffffff?text=2" alt="Görsel 2" class="w-full h-full object-cover">
                            </button>
                            <button class="relative flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 border-gray-200">
                                <img src="https://via.placeholder.com/80x80/f59e0b/ffffff?text=3" alt="Görsel 3" class="w-full h-full object-cover">
                            </button>
                        </div>
                    </div>

                    <!-- Product Info -->
                    <div class="space-y-6">
                        <!-- Category -->
                        <div>
                            <a href="#" class="text-sm text-primary hover:underline">Elektronik</a>
                        </div>

                        <!-- Title -->
                        <h1 class="text-3xl font-bold text-gray-900">Gaming Laptop 16GB RAM</h1>

                        <!-- Rating -->
                        <div class="flex items-center space-x-2">
                            <div class="flex items-center">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-gray-300"></i>
                            </div>
                            <span class="text-sm text-gray-600">4.2 (12 değerlendirme)</span>
                        </div>

                        <!-- Price -->
                        <div class="space-y-2">
                            <div class="flex items-center space-x-3">
                                <span class="text-3xl font-bold text-gray-900">₺12.999,99</span>
                                <span class="text-xl text-gray-500 line-through">₺17.333,32</span>
                            </div>
                            <p class="text-green-600 font-medium">₺4.333,33 tasarruf ediyorsunuz!</p>
                        </div>

                        <!-- Stock Status -->
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                            <span class="font-medium text-green-600">Stokta (5 adet)</span>
                        </div>

                        <!-- Quantity and Add to Cart -->
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <span class="font-medium">Adet:</span>
                                <div class="flex items-center border rounded-md">
                                    <button class="px-3 py-2 hover:bg-gray-50">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <span class="px-4 py-2 min-w-[60px] text-center">1</span>
                                    <button class="px-3 py-2 hover:bg-gray-50">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="flex space-x-4">
                                <button class="flex-1 bg-primary text-white py-3 px-6 rounded-md hover:bg-primary/90 transition-colors">
                                    <i class="fas fa-shopping-cart mr-2"></i>Sepete Ekle
                                </button>
                                <button class="border border-gray-300 py-3 px-6 rounded-md hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-6 border-t">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-truck text-2xl text-primary"></i>
                                <div>
                                    <p class="font-medium text-sm">Ücretsiz Kargo</p>
                                    <p class="text-xs text-gray-600">150₺ üzeri siparişlerde</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-shield-alt text-2xl text-primary"></i>
                                <div>
                                    <p class="font-medium text-sm">Güvenli Ödeme</p>
                                    <p class="text-xs text-gray-600">SSL sertifikalı</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-undo text-2xl text-primary"></i>
                                <div>
                                    <p class="font-medium text-sm">Kolay İade</p>
                                    <p class="text-xs text-gray-600">14 gün içinde</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Details Tabs -->
                <div class="w-full">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button class="tab-button active border-b-2 border-primary text-primary py-2 px-1 font-medium text-sm" onclick="showTab('description')">
                                Açıklama
                            </button>
                            <button class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm" onclick="showTab('specifications')">
                                Özellikler
                            </button>
                            <button class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 py-2 px-1 font-medium text-sm" onclick="showTab('reviews')">
                                Değerlendirmeler (12)
                            </button>
                        </nav>
                    </div>

                    <div class="mt-6">
                        <div id="description" class="tab-content">
                            <div class="card p-6">
                                <div class="prose max-w-none">
                                    <p class="text-gray-700 mb-4">
                                        Bu yüksek performanslı gaming laptop, en son teknoloji ile donatılmış olup,
                                        oyun severler ve profesyoneller için tasarlanmıştır.
                                    </p>
                                    <ul class="list-disc list-inside space-y-2 text-gray-700">
                                        <li>16GB DDR4 RAM</li>
                                        <li>512GB NVMe SSD</li>
                                        <li>NVIDIA GeForce RTX 3060 6GB</li>
                                        <li>15.6" Full HD 144Hz Display</li>
                                        <li>Intel Core i7-11800H İşlemci</li>
                                        <li>RGB Backlit Klavye</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div id="specifications" class="tab-content hidden">
                            <div class="card p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h4 class="font-medium mb-2">Genel Bilgiler</h4>
                                        <dl class="space-y-2 text-sm">
                                            <div class="flex justify-between">
                                                <dt class="text-gray-600">SKU:</dt>
                                                <dd>LAPTOP-001</dd>
                                            </div>
                                            <div class="flex justify-between">
                                                <dt class="text-gray-600">Kategori:</dt>
                                                <dd>Elektronik</dd>
                                            </div>
                                            <div class="flex justify-between">
                                                <dt class="text-gray-600">Stok:</dt>
                                                <dd>5 adet</dd>
                                            </div>
                                            <div class="flex justify-between">
                                                <dt class="text-gray-600">Ağırlık:</dt>
                                                <dd>2.3 kg</dd>
                                            </div>
                                            <div class="flex justify-between">
                                                <dt class="text-gray-600">Boyutlar:</dt>
                                                <dd>35.9 x 25.9 x 2.4 cm</dd>
                                            </div>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="reviews" class="tab-content hidden">
                            <div class="card p-6">
                                <div class="space-y-6">
                                    <div class="border-b pb-6">
                                        <div class="flex items-start justify-between mb-2">
                                            <div class="flex items-center space-x-2">
                                                <span class="font-medium">Ahmet K.</span>
                                                <div class="flex items-center">
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                </div>
                                            </div>
                                            <span class="text-sm text-gray-600">2 gün önce</span>
                                        </div>
                                        <h4 class="font-medium mb-2">Mükemmel performans!</h4>
                                        <p class="text-gray-700">Oyunlarda harika performans veriyor. Hızlı ve sessiz çalışıyor.</p>
                                    </div>
                                    <div class="border-b pb-6">
                                        <div class="flex items-start justify-between mb-2">
                                            <div class="flex items-center space-x-2">
                                                <span class="font-medium">Zeynep M.</span>
                                                <div class="flex items-center">
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-yellow-400 text-sm"></i>
                                                    <i class="fas fa-star text-gray-300 text-sm"></i>
                                                </div>
                                            </div>
                                            <span class="text-sm text-gray-600">1 hafta önce</span>
                                        </div>
                                        <h4 class="font-medium mb-2">Çok memnunum</h4>
                                        <p class="text-gray-700">Fiyat performans açısından çok iyi. Tavsiye ederim.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">E-Marketing</h3>
                    <p class="text-gray-400">Modern e-ticaret deneyimi için güvenilir adresiniz.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Hızlı Linkler</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" onclick="showPage('shop')" class="hover:text-white">Ürünler</a></li>
                        <li><a href="#" class="hover:text-white">Kategoriler</a></li>
                        <li><a href="#" class="hover:text-white">Hakkımızda</a></li>
                        <li><a href="#" class="hover:text-white">İletişim</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Müşteri Hizmetleri</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Yardım</a></li>
                        <li><a href="#" class="hover:text-white">İade & Değişim</a></li>
                        <li><a href="#" class="hover:text-white">Kargo Bilgileri</a></li>
                        <li><a href="#" class="hover:text-white">SSS</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">İletişim</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>Email: <EMAIL></li>
                        <li>Telefon: +90 (212) 123 45 67</li>
                        <li>Adres: İstanbul, Türkiye</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 E-Marketing. Tüm hakları saklıdır.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Modal -->
    <div id="cart-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b">
                    <h2 class="text-xl font-semibold">Sepetim</h2>
                    <button onclick="toggleCart()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div id="cart-content" class="p-6 max-h-96 overflow-y-auto">
                    <!-- Cart items will be loaded here -->
                </div>

                <!-- Modal Footer -->
                <div class="border-t p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-lg font-semibold">Toplam:</span>
                        <span id="cart-total" class="text-xl font-bold text-primary">₺0,00</span>
                    </div>
                    <div class="flex space-x-4">
                        <button onclick="toggleCart()" class="flex-1 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            Alışverişe Devam
                        </button>
                        <button class="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90">
                            Sepeti Onayla
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let dataBridge;
        let products = [];
        let categories = [];
        let cart = [];
        let currentCategory = 'all';
        let searchQuery = '';

        // Initialize DataBridge and load data
        function initializeData() {
            dataBridge = new DataBridge();
            products = dataBridge.getProducts() || [];
            categories = dataBridge.getData()?.categories || [];
            cart = JSON.parse(localStorage.getItem('preview-cart') || '[]');

            // Listen for data changes
            window.addEventListener('databridge:change', () => {
                products = dataBridge.getProducts() || [];
                categories = dataBridge.getData()?.categories || [];
                renderProducts();
                updateCartDisplay();
            });

            renderProducts();
            updateCartDisplay();
        }

        // Cart functions
        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;

            const existingItem = cart.find(item => item.productId === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    productId: productId,
                    quantity: 1,
                    product: product
                });
            }

            localStorage.setItem('preview-cart', JSON.stringify(cart));
            updateCartDisplay();
            showNotification(`${product.name} sepete eklendi!`);
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.productId !== productId);
            localStorage.setItem('preview-cart', JSON.stringify(cart));
            updateCartDisplay();
        }

        function updateCartQuantity(productId, quantity) {
            const item = cart.find(item => item.productId === productId);
            if (item) {
                if (quantity <= 0) {
                    removeFromCart(productId);
                } else {
                    item.quantity = quantity;
                    localStorage.setItem('preview-cart', JSON.stringify(cart));
                    updateCartDisplay();
                }
            }
        }

        function updateCartDisplay() {
            const cartCount = cart.reduce((sum, item) => sum + item.quantity, 0);
            const cartButton = document.querySelector('.cart-button span');
            if (cartButton) {
                cartButton.textContent = cartCount;
                cartButton.style.display = cartCount > 0 ? 'flex' : 'none';
            }

            // Update cart modal content
            updateCartModal();
        }

        function toggleCart() {
            const modal = document.getElementById('cart-modal');
            modal.classList.toggle('hidden');
        }

        function updateCartModal() {
            const cartContent = document.getElementById('cart-content');
            const cartTotal = document.getElementById('cart-total');

            if (cart.length === 0) {
                cartContent.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-shopping-cart text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">Sepetiniz boş</p>
                    </div>
                `;
                cartTotal.textContent = '₺0,00';
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.quantity * item.product.price), 0);
            cartTotal.textContent = `₺${total.toFixed(2)}`;

            cartContent.innerHTML = cart.map(item => `
                <div class="flex items-center space-x-4 p-4 border-b">
                    <img src="${item.product.images?.[0]?.url || 'https://via.placeholder.com/80x80/3b82f6/ffffff?text=' + encodeURIComponent(item.product.name)}"
                         alt="${item.product.name}"
                         class="w-16 h-16 object-cover rounded">
                    <div class="flex-1">
                        <h4 class="font-medium">${item.product.name}</h4>
                        <p class="text-sm text-gray-600">₺${item.product.price}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="updateCartQuantity('${item.productId}', ${item.quantity - 1})"
                                class="w-8 h-8 flex items-center justify-center border rounded hover:bg-gray-50">
                            <i class="fas fa-minus text-xs"></i>
                        </button>
                        <span class="w-8 text-center">${item.quantity}</span>
                        <button onclick="updateCartQuantity('${item.productId}', ${item.quantity + 1})"
                                class="w-8 h-8 flex items-center justify-center border rounded hover:bg-gray-50">
                            <i class="fas fa-plus text-xs"></i>
                        </button>
                    </div>
                    <button onclick="removeFromCart('${item.productId}')"
                            class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        // Product rendering
        function renderProducts() {
            const featuredContainer = document.getElementById('featured-products');
            const shopContainer = document.getElementById('shop-products');

            if (featuredContainer) {
                renderFeaturedProducts(featuredContainer);
            }

            if (shopContainer) {
                renderShopProducts(shopContainer);
            }

            renderCategoryFilters();
        }

        function renderCategoryFilters() {
            const categoryFiltersContainer = document.getElementById('category-filters');
            if (!categoryFiltersContainer) return;

            const categoryCount = {};
            products.forEach(product => {
                const categoryId = product.categoryId || 'uncategorized';
                categoryCount[categoryId] = (categoryCount[categoryId] || 0) + 1;
            });

            categoryFiltersContainer.innerHTML = categories.map(category => `
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="radio" name="category" value="${category.id}"
                           onchange="filterByCategory('${category.id}')"
                           class="rounded border-gray-300 text-primary focus:ring-primary">
                    <span class="text-sm">${category.name} (${categoryCount[category.id] || 0})</span>
                </label>
            `).join('');
        }

        function renderFeaturedProducts(container) {
            const featuredProducts = products.slice(0, 6);
            container.innerHTML = featuredProducts.map(product => `
                <div class="card overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                    <div class="relative aspect-square overflow-hidden">
                        <img src="${product.images?.[0]?.url || 'https://via.placeholder.com/300x300/3b82f6/ffffff?text=' + encodeURIComponent(product.name)}"
                             alt="${product.name}"
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        <button class="absolute top-2 right-2 bg-white/80 hover:bg-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-lg mb-2">${product.name}</h3>
                        <p class="text-gray-600 text-sm mb-3">${product.description || ''}</p>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="font-bold text-lg">₺${product.price}</span>
                            ${product.originalPrice ? `<span class="text-sm text-gray-500 line-through">₺${product.originalPrice}</span>` : ''}
                        </div>
                        <button onclick="addToCart('${product.id}')" class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary/90 transition-colors">
                            <i class="fas fa-shopping-cart mr-2"></i>Sepete Ekle
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function renderShopProducts(container) {
            let filteredProducts = products;

            // Apply category filter
            if (currentCategory !== 'all') {
                filteredProducts = filteredProducts.filter(p => p.categoryId === currentCategory);
            }

            // Apply search filter
            if (searchQuery) {
                filteredProducts = filteredProducts.filter(p =>
                    p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (p.description && p.description.toLowerCase().includes(searchQuery.toLowerCase()))
                );
            }

            container.innerHTML = filteredProducts.map(product => `
                <div class="card overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                    <div class="relative aspect-square overflow-hidden">
                        <img src="${product.images?.[0]?.url || 'https://via.placeholder.com/300x300/3b82f6/ffffff?text=' + encodeURIComponent(product.name)}"
                             alt="${product.name}"
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        <button class="absolute top-2 right-2 bg-white/80 hover:bg-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-lg mb-2">${product.name}</h3>
                        <p class="text-gray-600 text-sm mb-3">${product.description || ''}</p>
                        <div class="flex items-center gap-2 mb-3">
                            <span class="font-bold text-lg">₺${product.price}</span>
                            ${product.originalPrice ? `<span class="text-sm text-gray-500 line-through">₺${product.originalPrice}</span>` : ''}
                        </div>
                        <button onclick="addToCart('${product.id}')" class="w-full bg-primary text-white py-2 rounded-md hover:bg-primary/90 transition-colors">
                            <i class="fas fa-shopping-cart mr-2"></i>Sepete Ekle
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Search and filter functions
        function searchProducts(query) {
            searchQuery = query;
            renderProducts();
        }

        function filterByCategory(categoryId) {
            currentCategory = categoryId;
            renderProducts();
        }

        // Notification system
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-md text-white font-medium transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-section').forEach(page => {
                page.classList.add('hidden');
            });

            // Show selected page
            document.getElementById(pageId).classList.remove('hidden');

            // Update navigation active state
            document.querySelectorAll('nav a').forEach(link => {
                link.classList.remove('text-primary');
                link.classList.add('text-gray-700');
            });

            // Scroll to top
            window.scrollTo(0, 0);
        }

        // Tab functionality for product detail page
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Show selected tab content
            document.getElementById(tabId).classList.remove('hidden');

            // Update tab button states
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Activate clicked tab button
            event.target.classList.add('active', 'border-primary', 'text-primary');
            event.target.classList.remove('border-transparent', 'text-gray-500');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeData();
            showPage('home');
        });
    </script>
</body>
</html>