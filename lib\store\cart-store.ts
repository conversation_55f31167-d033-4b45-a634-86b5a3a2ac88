import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { CartItem, Product } from '@/types'
import { createClient } from '@/lib/supabase/client'

interface CartState {
  // Cart data
  items: (CartItem & { product: Product })[]
  isOpen: boolean
  
  // Loading states
  isLoading: boolean
  isUpdating: boolean
  
  // Computed values
  totalItems: number
  totalPrice: number
  
  // Actions
  setItems: (items: (CartItem & { product: Product })[]) => void
  setOpen: (open: boolean) => void
  setLoading: (loading: boolean) => void
  
  // Cart actions
  addItem: (productId: string, quantity?: number) => Promise<{ success: boolean; error?: string }>
  removeItem: (itemId: string) => Promise<{ success: boolean; error?: string }>
  updateQuantity: (itemId: string, quantity: number) => Promise<{ success: boolean; error?: string }>
  clearCart: () => Promise<{ success: boolean; error?: string }>
  
  // Fetch cart
  fetchCart: () => Promise<void>
  
  // Sync with server
  syncWithServer: () => Promise<void>
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      isOpen: false,
      isLoading: false,
      isUpdating: false,
      totalItems: 0,
      totalPrice: 0,
      
      // Setters
      setItems: (items) => {
        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
        const totalPrice = items.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
        
        set({ 
          items, 
          totalItems, 
          totalPrice 
        })
      },
      
      setOpen: (open) => set({ isOpen: open }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      // Cart actions
      addItem: async (productId, quantity = 1) => {
        set({ isUpdating: true })
        
        try {
          const supabase = createClient()
          const { data: { user } } = await supabase.auth.getUser()
          
          if (!user) {
            // Handle guest cart (localStorage)
            const { items } = get()
            const existingItem = items.find(item => item.productId === productId)
            
            if (existingItem) {
              return get().updateQuantity(existingItem.id, existingItem.quantity + quantity)
            } else {
              // Fetch product data
              const { data: productData } = await supabase
                .from('products')
                .select(`
                  *,
                  category:categories(*),
                  images:product_images(*)
                `)
                .eq('id', productId)
                .single()
              
              if (!productData) {
                return { success: false, error: 'Ürün bulunamadı' }
              }
              
              const newItem: CartItem & { product: Product } = {
                id: `guest-${Date.now()}`,
                userId: 'guest',
                productId,
                quantity,
                createdAt: new Date(),
                updatedAt: new Date(),
                product: productData,
              }
              
              const newItems = [...items, newItem]
              get().setItems(newItems)
              
              return { success: true }
            }
          } else {
            // Handle authenticated user cart
            const { data: existingItem } = await supabase
              .from('cart_items')
              .select('*')
              .eq('user_id', user.id)
              .eq('product_id', productId)
              .single()
            
            if (existingItem) {
              // Update existing item
              const { error } = await supabase
                .from('cart_items')
                .update({ 
                  quantity: existingItem.quantity + quantity,
                  updated_at: new Date().toISOString()
                })
                .eq('id', existingItem.id)
              
              if (error) {
                return { success: false, error: error.message }
              }
            } else {
              // Create new item
              const { error } = await supabase
                .from('cart_items')
                .insert({
                  user_id: user.id,
                  product_id: productId,
                  quantity,
                })
              
              if (error) {
                return { success: false, error: error.message }
              }
            }
            
            // Refresh cart
            await get().fetchCart()
            return { success: true }
          }
        } catch (error) {
          console.error('Add to cart error:', error)
          return { success: false, error: 'Sepete eklenirken hata oluştu' }
        } finally {
          set({ isUpdating: false })
        }
      },
      
      removeItem: async (itemId) => {
        set({ isUpdating: true })
        
        try {
          const supabase = createClient()
          const { data: { user } } = await supabase.auth.getUser()
          
          if (!user) {
            // Handle guest cart
            const { items } = get()
            const newItems = items.filter(item => item.id !== itemId)
            get().setItems(newItems)
            return { success: true }
          } else {
            // Handle authenticated user cart
            const { error } = await supabase
              .from('cart_items')
              .delete()
              .eq('id', itemId)
            
            if (error) {
              return { success: false, error: error.message }
            }
            
            // Refresh cart
            await get().fetchCart()
            return { success: true }
          }
        } catch (error) {
          console.error('Remove from cart error:', error)
          return { success: false, error: 'Sepetten çıkarılırken hata oluştu' }
        } finally {
          set({ isUpdating: false })
        }
      },
      
      updateQuantity: async (itemId, quantity) => {
        if (quantity <= 0) {
          return get().removeItem(itemId)
        }
        
        set({ isUpdating: true })
        
        try {
          const supabase = createClient()
          const { data: { user } } = await supabase.auth.getUser()
          
          if (!user) {
            // Handle guest cart
            const { items } = get()
            const newItems = items.map(item => 
              item.id === itemId 
                ? { ...item, quantity, updatedAt: new Date() }
                : item
            )
            get().setItems(newItems)
            return { success: true }
          } else {
            // Handle authenticated user cart
            const { error } = await supabase
              .from('cart_items')
              .update({ 
                quantity,
                updated_at: new Date().toISOString()
              })
              .eq('id', itemId)
            
            if (error) {
              return { success: false, error: error.message }
            }
            
            // Refresh cart
            await get().fetchCart()
            return { success: true }
          }
        } catch (error) {
          console.error('Update quantity error:', error)
          return { success: false, error: 'Miktar güncellenirken hata oluştu' }
        } finally {
          set({ isUpdating: false })
        }
      },
      
      clearCart: async () => {
        set({ isUpdating: true })
        
        try {
          const supabase = createClient()
          const { data: { user } } = await supabase.auth.getUser()
          
          if (!user) {
            // Handle guest cart
            get().setItems([])
            return { success: true }
          } else {
            // Handle authenticated user cart
            const { error } = await supabase
              .from('cart_items')
              .delete()
              .eq('user_id', user.id)
            
            if (error) {
              return { success: false, error: error.message }
            }
            
            get().setItems([])
            return { success: true }
          }
        } catch (error) {
          console.error('Clear cart error:', error)
          return { success: false, error: 'Sepet temizlenirken hata oluştu' }
        } finally {
          set({ isUpdating: false })
        }
      },
      
      fetchCart: async () => {
        set({ isLoading: true })
        
        try {
          const supabase = createClient()
          const { data: { user } } = await supabase.auth.getUser()
          
          if (!user) {
            set({ isLoading: false })
            return
          }
          
          const { data: cartItems } = await supabase
            .from('cart_items')
            .select(`
              *,
              product:products(
                *,
                category:categories(*),
                images:product_images(*)
              )
            `)
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
          
          if (cartItems) {
            get().setItems(cartItems as (CartItem & { product: Product })[])
          }
        } catch (error) {
          console.error('Fetch cart error:', error)
        } finally {
          set({ isLoading: false })
        }
      },
      
      syncWithServer: async () => {
        try {
          const supabase = createClient()
          const { data: { user } } = await supabase.auth.getUser()
          
          if (!user) return
          
          const { items } = get()
          const guestItems = items.filter(item => item.userId === 'guest')
          
          if (guestItems.length === 0) return
          
          // Sync guest cart items to server
          for (const item of guestItems) {
            await supabase
              .from('cart_items')
              .upsert({
                user_id: user.id,
                product_id: item.productId,
                quantity: item.quantity,
              })
          }
          
          // Fetch updated cart
          await get().fetchCart()
        } catch (error) {
          console.error('Sync cart error:', error)
        }
      },
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({ 
        items: state.items.filter(item => item.userId === 'guest') 
      }),
    }
  )
)
